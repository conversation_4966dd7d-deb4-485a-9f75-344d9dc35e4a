{% extends "responsive_base.html" %}

{% block title %}Login - {{ school_info.school_name or 'Hillview School' }}{% endblock %}

{% block meta_description %}Secure login portal for {{ school_info.school_name or 'Hillview School' }} Management System{% endblock %}

{% block body_class %}login-page{% endblock %}

{% block extra_css %}
<style>
/* Login Page Specific Styles */
.login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-container {
    width: 100%;
    max-width: 400px;
    margin: var(--space-md);
}

.login-card {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    animation: slideUp 0.5s ease-out;
}

.login-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: var(--space-xl);
    text-align: center;
}

.login-logo {
    width: 64px;
    height: 64px;
    margin: 0 auto var(--space-md);
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-2xl);
}

.login-title {
    font-size: var(--text-xl);
    font-weight: 600;
    margin: 0 0 var(--space-sm);
}

.login-subtitle {
    font-size: var(--text-sm);
    opacity: 0.9;
    margin: 0;
}

.login-body {
    padding: var(--space-xl);
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.form-group {
    position: relative;
}

.form-input {
    width: 100%;
    padding: var(--space-md) var(--space-lg);
    padding-left: 3rem;
    border: 2px solid #e5e7eb;
    border-radius: var(--radius-lg);
    font-size: var(--text-base);
    transition: all 0.2s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-icon {
    position: absolute;
    left: var(--space-md);
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: var(--text-lg);
}

.login-button {
    width: 100%;
    padding: var(--space-md) var(--space-lg);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--text-base);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: var(--touch-target-comfortable);
}

.login-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.login-button:active {
    transform: translateY(0);
}

.login-button.loading {
    pointer-events: none;
    opacity: 0.8;
}

.login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--space-md);
    font-size: var(--text-sm);
}

.remember-me {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.forgot-password {
    color: #667eea;
    text-decoration: none;
}

.forgot-password:hover {
    text-decoration: underline;
}

.login-footer {
    padding: var(--space-lg) var(--space-xl);
    background: #f8f9fa;
    text-align: center;
    border-top: 1px solid #e5e7eb;
}

.portal-links {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-md);
    justify-content: center;
    margin-bottom: var(--space-md);
}

.portal-link {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-sm) var(--space-md);
    background: white;
    color: #667eea;
    text-decoration: none;
    border-radius: var(--radius-md);
    border: 1px solid #e5e7eb;
    font-size: var(--text-sm);
    transition: all 0.2s ease;
    min-height: var(--touch-target-min);
}

.portal-link:hover {
    background: #667eea;
    color: white;
    transform: translateY(-1px);
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile Responsive Adjustments */
@media (max-width: 479px) {
    .login-container {
        margin: var(--space-sm);
    }
    
    .login-header,
    .login-body,
    .login-footer {
        padding: var(--space-lg);
    }
    
    .login-title {
        font-size: var(--text-lg);
    }
    
    .portal-links {
        flex-direction: column;
        gap: var(--space-sm);
    }
    
    .portal-link {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 360px) {
    .login-container {
        margin: var(--space-xs);
    }
    
    .login-header,
    .login-body,
    .login-footer {
        padding: var(--space-md);
    }
}
</style>
{% endblock %}

{% block navigation %}
<!-- No navigation on login page -->
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card card">
        <div class="login-header">
            <div class="login-logo">
                {% if school_info and school_info.logo_filename %}
                <img src="{{ url_for('static', filename='uploads/logos/' + school_info.logo_filename) }}" 
                     alt="{{ school_info.school_name or 'School' }} Logo" 
                     class="img-responsive" 
                     style="width: 100%; height: 100%; object-fit: contain;">
                {% else %}
                <i class="fas fa-graduation-cap"></i>
                {% endif %}
            </div>
            <h1 class="login-title">{{ school_info.school_name or 'Hillview School' }}</h1>
            <p class="login-subtitle">School Management System</p>
        </div>
        
        <div class="login-body">
            <form class="login-form form-responsive" method="POST" action="{{ url_for('auth.login') }}">
                <div class="form-group">
                    <div class="form-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <input type="text" 
                           name="username" 
                           class="form-input" 
                           placeholder="Username" 
                           required 
                           autocomplete="username"
                           aria-label="Username">
                </div>
                
                <div class="form-group">
                    <div class="form-icon">
                        <i class="fas fa-lock"></i>
                    </div>
                    <input type="password" 
                           name="password" 
                           class="form-input" 
                           placeholder="Password" 
                           required 
                           autocomplete="current-password"
                           aria-label="Password">
                </div>
                
                <button type="submit" class="login-button btn btn-primary">
                    <span class="button-text">Sign In</span>
                    <span class="button-spinner" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                    </span>
                </button>
                
                <div class="login-options">
                    <label class="remember-me">
                        <input type="checkbox" name="remember" class="form-checkbox">
                        <span>Remember me</span>
                    </label>
                    <a href="#" class="forgot-password">Forgot password?</a>
                </div>
            </form>
        </div>
        
        <div class="login-footer">
            <div class="portal-links">
                <a href="{{ url_for('auth.headteacher_login') }}" class="portal-link">
                    <i class="fas fa-user-tie"></i>
                    <span>Headteacher</span>
                </a>
                <a href="{{ url_for('auth.classteacher_login') }}" class="portal-link">
                    <i class="fas fa-chalkboard-teacher"></i>
                    <span>Class Teacher</span>
                </a>
                <a href="{{ url_for('auth.teacher_login') }}" class="portal-link">
                    <i class="fas fa-user-graduate"></i>
                    <span>Subject Teacher</span>
                </a>
                <a href="{{ url_for('auth.parent_login') }}" class="portal-link">
                    <i class="fas fa-users"></i>
                    <span>Parent Portal</span>
                </a>
            </div>
            <p class="text-xs" style="color: #6b7280; margin: 0;">
                Secure access to your school management system
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block page_js %}
// Login form enhancements
const loginForm = document.querySelector('.login-form');
const loginButton = document.querySelector('.login-button');

if (loginForm && loginButton) {
    loginForm.addEventListener('submit', function(e) {
        // Show loading state
        loginButton.classList.add('loading');
        loginButton.querySelector('.button-text').style.display = 'none';
        loginButton.querySelector('.button-spinner').style.display = 'inline';
        loginButton.disabled = true;
    });
}

// Auto-focus first input
const firstInput = document.querySelector('.form-input');
if (firstInput) {
    firstInput.focus();
}
{% endblock %}
