#!/usr/bin/env python3
"""
Sample Data Generator for Enhanced Student Promotion System
===========================================================

This script creates comprehensive sample data to test the enhanced student promotion features.
It creates students across all grades with realistic data for testing promotion scenarios.

Run this script to populate the database with test data for promotion system testing.
"""

import sys
import os
from datetime import datetime, date
import random

# Add the parent directory to the path to import the app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from extensions import db
from models.academic import Student, Grade, Stream, Subject, Mark
from models.user import Teacher
from models.school_setup import SchoolSetup

def create_sample_promotion_data():
    """Create comprehensive sample data for promotion system testing."""
    
    app = create_app()
    
    with app.app_context():
        print("🚀 Creating Enhanced Sample Data for Student Promotion System...")
        
        # Ensure we have grades and streams
        create_grades_and_streams()
        
        # Create sample students across all grades
        create_sample_students()
        
        # Create sample marks for realistic promotion decisions
        create_sample_marks()
        
        print("✅ Sample data creation completed successfully!")
        print("\n📊 SAMPLE DATA SUMMARY:")
        print(f"   • Total Students: {Student.query.count()}")
        print(f"   • Total Grades: {Grade.query.count()}")
        print(f"   • Total Streams: {Stream.query.count()}")
        print(f"   • Students by Grade:")
        
        for grade in Grade.query.order_by(Grade.level).all():
            count = Student.query.filter_by(grade_id=grade.id).count()
            print(f"     - Grade {grade.name}: {count} students")

def create_grades_and_streams():
    """Ensure all grades and streams exist."""
    
    # Create grades if they don't exist
    grade_data = [
        {'name': '1', 'level': 1, 'education_level': 'Lower Primary'},
        {'name': '2', 'level': 2, 'education_level': 'Lower Primary'},
        {'name': '3', 'level': 3, 'education_level': 'Lower Primary'},
        {'name': '4', 'level': 4, 'education_level': 'Upper Primary'},
        {'name': '5', 'level': 5, 'education_level': 'Upper Primary'},
        {'name': '6', 'level': 6, 'education_level': 'Upper Primary'},
        {'name': '7', 'level': 7, 'education_level': 'Junior Secondary'},
        {'name': '8', 'level': 8, 'education_level': 'Junior Secondary'},
        {'name': '9', 'level': 9, 'education_level': 'Junior Secondary'},
    ]
    
    for grade_info in grade_data:
        grade = Grade.query.filter_by(name=grade_info['name']).first()
        if not grade:
            grade = Grade(
                name=grade_info['name'],
                level=grade_info['level'],
                education_level=grade_info['education_level']
            )
            db.session.add(grade)
    
    db.session.commit()
    
    # Create streams for each grade
    stream_names = ['A', 'B', 'C']
    
    for grade in Grade.query.all():
        for stream_name in stream_names:
            stream = Stream.query.filter_by(name=stream_name, grade_id=grade.id).first()
            if not stream:
                stream = Stream(
                    name=stream_name,
                    grade_id=grade.id,
                    capacity=40
                )
                db.session.add(stream)
    
    db.session.commit()

def create_sample_students():
    """Create realistic sample students across all grades."""
    
    # Sample student names
    first_names = [
        'John', 'Mary', 'Peter', 'Grace', 'David', 'Sarah', 'Michael', 'Faith',
        'James', 'Ruth', 'Daniel', 'Joy', 'Samuel', 'Mercy', 'Joseph', 'Esther',
        'Paul', 'Rebecca', 'Mark', 'Hannah', 'Luke', 'Naomi', 'Matthew', 'Lydia',
        'Stephen', 'Priscilla', 'Timothy', 'Deborah', 'Philip', 'Martha'
    ]
    
    last_names = [
        'Kiprotich', 'Wanjiku', 'Mwangi', 'Achieng', 'Kiplagat', 'Njeri',
        'Ochieng', 'Wambui', 'Rotich', 'Nyambura', 'Kiptoo', 'Wairimu',
        'Cheruiyot', 'Wanjiru', 'Koech', 'Muthoni', 'Lagat', 'Njoki',
        'Ruto', 'Wangari', 'Sang', 'Gathoni', 'Bett', 'Wawira'
    ]
    
    # Create students for each grade and stream
    admission_counter = 1000
    
    for grade in Grade.query.order_by(Grade.level).all():
        streams = Stream.query.filter_by(grade_id=grade.id).all()
        
        for stream in streams:
            # Create 15-25 students per stream
            num_students = random.randint(15, 25)
            
            for i in range(num_students):
                # Generate student data
                first_name = random.choice(first_names)
                last_name = random.choice(last_names)
                full_name = f"{first_name} {last_name}"
                
                # Check if student already exists
                existing_student = Student.query.filter_by(
                    name=full_name,
                    grade_id=grade.id,
                    stream_id=stream.id
                ).first()
                
                if existing_student:
                    continue
                
                admission_counter += 1
                
                # Determine promotion eligibility based on grade and performance
                is_final_grade = (grade.level == 9)
                
                # Most students are eligible, some need review
                can_be_promoted = random.choice([True, True, True, True, False])  # 80% eligible
                
                # Create student
                student = Student(
                    name=full_name,
                    admission_number=f"HV{admission_counter:04d}",
                    grade_id=grade.id,
                    stream_id=stream.id,
                    gender=random.choice(['Male', 'Female']),
                    date_of_birth=date(2010 + grade.level, random.randint(1, 12), random.randint(1, 28)),
                    academic_year="2024-2025",
                    promotion_status='active',
                    is_eligible_for_promotion=can_be_promoted,
                    date_enrolled=date(2024 - grade.level + 1, 1, 15)
                )
                
                db.session.add(student)
    
    db.session.commit()

def create_sample_marks():
    """Create sample marks to support promotion decisions."""
    
    # Get or create sample subjects
    subjects_data = [
        'ENGLISH', 'KISWAHILI', 'MATHEMATICS', 'SCIENCE', 'SOCIAL_STUDIES',
        'CRE', 'CREATIVE_ARTS', 'PHYSICAL_EDUCATION'
    ]
    
    subjects = []
    for subject_name in subjects_data:
        subject = Subject.query.filter_by(name=subject_name).first()
        if not subject:
            subject = Subject(
                name=subject_name,
                code=subject_name[:3],
                education_level='All'
            )
            db.session.add(subject)
            subjects.append(subject)
        else:
            subjects.append(subject)
    
    db.session.commit()
    
    # Create marks for students (simplified for testing)
    students = Student.query.all()
    
    for student in students:
        for subject in subjects[:5]:  # Create marks for first 5 subjects
            # Generate realistic marks based on promotion eligibility
            if student.is_eligible_for_promotion:
                # Good performance - 60-95%
                marks = random.randint(60, 95)
            else:
                # Poor performance - 30-59%
                marks = random.randint(30, 59)
            
            # Check if mark already exists
            existing_mark = Mark.query.filter_by(
                student_id=student.id,
                subject_id=subject.id,
                term='Term 3',
                academic_year='2024-2025'
            ).first()
            
            if not existing_mark:
                mark = Mark(
                    student_id=student.id,
                    subject_id=subject.id,
                    marks=marks,
                    max_marks=100,
                    term='Term 3',
                    academic_year='2024-2025',
                    assessment_type='End of Term'
                )
                db.session.add(mark)
    
    db.session.commit()

if __name__ == '__main__':
    create_sample_promotion_data()
