<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=yes, maximum-scale=5.0"
    />
    <meta
      name="description"
      content="Headteacher Dashboard - {{ school_info.school_name or 'Hillview School' }} Management System"
    />
    <meta name="format-detection" content="telephone=no" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="theme-color" content="#667eea" />

    <title>
      Headteacher Dashboard - {{ school_info.school_name or 'Hillview School
      Management System' }}
    </title>

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" />

    <!-- Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Icons -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <!-- Legacy CSS (TEMPORARILY DISABLED for navbar debugging) -->
    <!-- <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/headteacher.css') }}"
    /> -->

    <style>
      /* Enhanced Modern Headteacher Dashboard Styles */
      :root {
        --primary-color: #667eea;
        --primary-dark: #5a67d8;
        --secondary-color: #764ba2;
        --success-color: #48bb78;
        --warning-color: #ed8936;
        --danger-color: #f56565;
        --info-color: #4299e1;
        --light-bg: #f8fafc;
        --white: #ffffff;
        --text-primary: #2d3748;
        --text-secondary: #718096;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        --radius-sm: 0.375rem;
        --radius-md: 0.5rem;
        --radius-lg: 0.75rem;
        --radius-xl: 1rem;
        --radius-2xl: 1.5rem;
      }

      body {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        color: var(--text-primary);
        line-height: 1.6;
        margin: 0;
        padding: 0;
        min-height: 100vh;
      }

      /* Enhanced table structure */
      .grade-row {
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        font-weight: 700;
        border-left: 4px solid var(--primary-color);
      }

      .stream-row {
        background-color: var(--white);
        transition: all 0.2s ease;
      }

      .stream-row:hover {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        transform: translateX(2px);
      }

      .grade-row td,
      .stream-row td {
        padding: 12px 16px;
        border-bottom: 1px solid var(--border-color);
        transition: all 0.2s ease;
      }

      .gender-male {
        color: var(--info-color);
        font-weight: 600;
        background: rgba(66, 153, 225, 0.1);
        padding: 4px 8px;
        border-radius: var(--radius-sm);
      }

      .gender-female {
        color: #e53e3e;
        font-weight: 600;
        background: rgba(229, 62, 62, 0.1);
        padding: 4px 8px;
        border-radius: var(--radius-sm);
      }

      /* Navigation */
      .navbar {
        background: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--secondary-color) 100%
        );
        padding: 1rem 0;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .navbar .container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 1rem;
        width: 100%;
        box-sizing: border-box;
      }

      .navbar-brand {
        color: white;
        font-size: 1.5rem;
        font-weight: 700;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .navbar-nav {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
        gap: 0.5rem;
        align-items: center;
        flex-wrap: nowrap;
        overflow: visible;
      }

      .nav-link {
        color: rgba(255, 255, 255, 0.9);
        text-decoration: none;
        font-weight: 500;
        padding: 0.5rem 0.75rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.3rem;
        white-space: nowrap;
        font-size: 0.9rem;
      }

      .nav-link:hover {
        color: white;
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-1px);
      }

      .logout-btn {
        color: white;
        text-decoration: none;
        font-weight: 600;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(220, 53, 69, 0.8);
        border: 1px solid rgba(220, 53, 69, 1);
        white-space: nowrap;
        min-width: 80px;
        justify-content: center;
      }

      .logout-btn:hover {
        color: white;
        background: rgba(220, 53, 69, 1);
        border-color: rgba(220, 53, 69, 1);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
      }

      /* Enhanced Dashboard Container */
      .dashboard-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 2rem;
        min-height: calc(100vh - 80px);
      }

      /* Enhanced Section Headers */
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--border-color);
        position: relative;
      }

      .section-header::after {
        content: "";
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 60px;
        height: 2px;
        background: linear-gradient(
          90deg,
          var(--primary-color),
          var(--secondary-color)
        );
      }

      .section-header h2 {
        font-size: 1.875rem;
        font-weight: 700;
        color: var(--text-primary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
      }

      /* Enhanced Dashboard Actions */
      .dashboard-actions {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
        justify-content: flex-end;
      }

      .action-btn {
        padding: 0.75rem 1.5rem;
        border-radius: var(--radius-lg);
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        cursor: pointer;
        border: none;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .action-btn:not(.action-btn-outline) {
        background: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--secondary-color) 100%
        );
        color: var(--white);
        box-shadow: var(--shadow-md);
      }

      .action-btn:not(.action-btn-outline):hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
      }

      .action-btn-outline {
        background: var(--white);
        color: var(--primary-color);
        border: 2px solid var(--primary-color);
        box-shadow: var(--shadow-sm);
      }

      .action-btn-outline:hover {
        background: var(--primary-color);
        color: var(--white);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
      }

      /* Performance Distribution */
      .performance-distribution {
        margin-bottom: 30px;
      }

      .distribution-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
      }

      .distribution-card {
        padding: 20px;
        border-radius: 12px;
        text-align: center;
        color: white;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }

      .distribution-card.grade-ee {
        background: linear-gradient(135deg, #28a745, #20c997);
      }

      .distribution-card.grade-me {
        background: linear-gradient(135deg, #17a2b8, #6f42c1);
      }

      .distribution-card.grade-ae {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
        color: #333;
      }

      .distribution-card.grade-be {
        background: linear-gradient(135deg, #dc3545, #e83e8c);
      }

      .distribution-label {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 10px;
      }

      .distribution-value {
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 8px;
      }

      .distribution-desc {
        font-size: 12px;
        opacity: 0.9;
      }

      /* New Dashboard Features Styling */
      .stat-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease;
      }

      .stat-card:hover {
        transform: translateY(-2px);
      }

      .stat-icon {
        font-size: 24px;
        margin-bottom: 10px;
      }

      .stat-number {
        font-size: 28px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 5px;
      }

      .stat-label {
        font-size: 14px;
        color: #6c757d;
        font-weight: 500;
      }

      /* Enhanced Calendar Overview */
      .calendar-overview {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 16px;
        padding: 24px;
        margin-bottom: 30px;
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
      }

      .term-header {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 2px solid var(--border-color);
      }

      .term-badge {
        display: flex;
        align-items: center;
        gap: 8px;
        background: linear-gradient(135deg, var(--success-color), #059669);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
        box-shadow: var(--shadow-sm);
      }

      .term-name {
        color: var(--text-primary);
        margin: 0;
        font-size: 1.5rem;
        font-weight: 700;
      }

      .assessments-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }

      .assessments-header h4 {
        display: flex;
        align-items: center;
        gap: 8px;
        color: var(--text-primary);
        margin: 0;
        font-size: 1.125rem;
        font-weight: 600;
      }

      .assessments-count {
        background: var(--primary-color);
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 0.875rem;
        font-weight: 600;
      }

      .assessments-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 16px;
      }

      .assessment-card {
        background: white;
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 16px;
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-sm);
      }

      .assessment-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        border-color: var(--primary-color);
      }

      .assessment-icon {
        width: 48px;
        height: 48px;
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--secondary-color)
        );
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
      }

      .assessment-details {
        flex: 1;
      }

      .assessment-name {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 4px;
      }

      .assessment-weight {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.875rem;
      }

      .weight-label {
        color: var(--text-secondary);
      }

      .weight-value {
        background: var(--warning-color);
        color: white;
        padding: 2px 8px;
        border-radius: 8px;
        font-weight: 600;
      }

      .status-badge {
        padding: 4px 8px;
        border-radius: 8px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
      }

      .status-active {
        background: var(--success-color);
        color: white;
      }

      .no-assessments {
        text-align: center;
        padding: 32px;
        color: var(--text-secondary);
      }

      .no-assessments i {
        font-size: 2rem;
        margin-bottom: 12px;
        color: var(--primary-color);
      }

      /* Performance Table Enhancements */
      .card-actions {
        display: flex;
        gap: 10px;
      }

      .filter-section {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
      }

      .filter-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
      }

      .filter-grid select {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: white;
      }

      .performance-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
      }

      .badge-excellent {
        background: #d4edda;
        color: #155724;
      }

      .badge-good {
        background: #d1ecf1;
        color: #0c5460;
      }

      .badge-average {
        background: #fff3cd;
        color: #856404;
      }

      .badge-below {
        background: #f8d7da;
        color: #721c24;
      }

      /* Performance count styling for detailed grading system */
      .count-ee1,
      .count-ee2 {
        color: #28a745;
        font-weight: 600;
        background: #d4edda;
        padding: 2px 6px;
        border-radius: 4px;
      }
      .count-me1,
      .count-me2 {
        color: #17a2b8;
        font-weight: 600;
        background: #d1ecf1;
        padding: 2px 6px;
        border-radius: 4px;
      }
      .count-ae1,
      .count-ae2 {
        color: #856404;
        font-weight: 600;
        background: #fff3cd;
        padding: 2px 6px;
        border-radius: 4px;
      }
      .count-be1,
      .count-be2 {
        color: #721c24;
        font-weight: 600;
        background: #f8d7da;
        padding: 2px 6px;
        border-radius: 4px;
      }

      /* Table responsiveness for more columns */
      .data-table {
        font-size: 10px;
        min-width: 1500px;
        width: 100%;
        table-layout: fixed;
      }

      .data-table th,
      .data-table td {
        padding: 4px 2px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .data-table th:first-child,
      .data-table td:first-child {
        text-align: left;
        width: 60px; /* Grade */
      }

      .data-table th:nth-child(2),
      .data-table td:nth-child(2) {
        width: 60px; /* Stream */
      }

      .data-table th:nth-child(3),
      .data-table td:nth-child(3) {
        width: 80px; /* Term */
      }

      .data-table th:nth-child(4),
      .data-table td:nth-child(4) {
        width: 100px; /* Assessment Type */
      }

      .data-table th:nth-child(5),
      .data-table td:nth-child(5) {
        width: 70px; /* Students */
      }

      .data-table th:nth-child(6),
      .data-table td:nth-child(6) {
        width: 120px; /* Class Average */
        font-weight: bold;
      }

      /* Grade count columns - make them smaller and uniform */
      .data-table th:nth-child(n + 7),
      .data-table td:nth-child(n + 7) {
        width: 50px;
        font-size: 9px;
      }

      .table-responsive {
        overflow-x: auto;
        max-width: 100%;
        border: 1px solid #dee2e6;
        border-radius: 8px;
      }

      /* Statistics Cards */
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }

      .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .stat-card:nth-child(2) {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }

      .stat-card:nth-child(3) {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      .stat-card:nth-child(4) {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }

      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
      }

      .stat-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.1);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .stat-card:hover::before {
        opacity: 1;
      }

      .stat-icon {
        font-size: 2.5rem;
        margin-right: 20px;
        opacity: 0.9;
      }

      .stat-content {
        flex: 1;
      }

      .stat-number {
        font-size: 2.2rem;
        font-weight: bold;
        margin-bottom: 5px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      /* Enhanced System Alerts */
      .alerts-summary {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .alerts-count {
        background: var(--warning-color);
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 0.875rem;
        font-weight: 600;
      }

      .system-alerts-container {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      .system-alert {
        background: white;
        border-radius: 12px;
        padding: 20px;
        display: flex;
        align-items: flex-start;
        gap: 16px;
        box-shadow: var(--shadow-sm);
        border-left: 4px solid transparent;
        transition: all 0.3s ease;
      }

      .system-alert:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
      }

      .system-alert.alert-warning {
        border-left-color: var(--warning-color);
        background: linear-gradient(135deg, #fff8e1 0%, #ffffff 100%);
      }

      .system-alert.alert-critical {
        border-left-color: var(--danger-color);
        background: linear-gradient(135deg, #ffebee 0%, #ffffff 100%);
      }

      .system-alert.alert-info {
        border-left-color: var(--info-color);
        background: linear-gradient(135deg, #e3f2fd 0%, #ffffff 100%);
      }

      .alert-indicator {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        min-width: 60px;
      }

      .alert-indicator .alert-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        color: white;
      }

      .alert-warning .alert-icon {
        background: var(--warning-color);
      }

      .alert-critical .alert-icon {
        background: var(--danger-color);
      }

      .alert-info .alert-icon {
        background: var(--info-color);
      }

      .alert-priority {
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        padding: 2px 8px;
        border-radius: 8px;
        color: white;
      }

      .alert-warning .alert-priority {
        background: var(--warning-color);
      }

      .alert-critical .alert-priority {
        background: var(--danger-color);
      }

      .alert-info .alert-priority {
        background: var(--info-color);
      }

      .alert-content {
        flex: 1;
      }

      .alert-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 8px;
      }

      .alert-title {
        font-weight: 600;
        color: var(--text-primary);
        font-size: 1.125rem;
      }

      .alert-timestamp {
        display: flex;
        align-items: center;
        gap: 4px;
        color: var(--text-secondary);
        font-size: 0.875rem;
      }

      .alert-message {
        color: var(--text-secondary);
        line-height: 1.5;
        margin-bottom: 12px;
      }

      .alert-action {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        background: rgba(0, 0, 0, 0.05);
        padding: 12px;
        border-radius: 8px;
        font-size: 0.875rem;
        color: var(--text-secondary);
      }

      .alert-actions {
        display: flex;
        flex-direction: column;
        gap: 8px;
        min-width: 100px;
      }

      .alert-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        padding: 8px 12px;
        border: none;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .btn-dismiss {
        background: var(--danger-color);
        color: white;
      }

      .btn-dismiss:hover {
        background: #dc2626;
        transform: translateY(-1px);
      }

      .btn-details {
        background: var(--primary-color);
        color: white;
      }

      .btn-details:hover {
        background: var(--secondary-color);
        transform: translateY(-1px);
      }

      .no-alerts-container {
        background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);
        border-radius: 16px;
        padding: 48px 24px;
        text-align: center;
        border: 1px solid var(--border-color);
      }

      .no-alerts-content i {
        font-size: 3rem;
        color: var(--success-color);
        margin-bottom: 16px;
      }

      .no-alerts-content h3 {
        color: var(--text-primary);
        margin-bottom: 8px;
        font-size: 1.25rem;
      }

      .no-alerts-content p {
        color: var(--text-secondary);
        margin: 0;
      }

      /* Enhanced Performance Heatmap */
      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 12px;
        border-bottom: 1px solid var(--border-color);
      }

      .chart-header h3 {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0;
        color: var(--text-primary);
        font-size: 1.25rem;
        font-weight: 600;
      }

      .chart-info {
        color: var(--text-secondary);
        cursor: help;
        transition: color 0.3s ease;
      }

      .chart-info:hover {
        color: var(--primary-color);
      }

      .heatmap-controls {
        margin-bottom: 20px;
      }

      .control-group {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .control-group label {
        display: flex;
        align-items: center;
        gap: 6px;
        font-weight: 500;
        color: var(--text-primary);
        font-size: 0.875rem;
      }

      .control-group select {
        padding: 8px 12px;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        background: white;
        color: var(--text-primary);
        font-size: 0.875rem;
        min-width: 200px;
        transition: all 0.3s ease;
      }

      .control-group select:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .heatmap-container {
        background: white;
        border-radius: 8px;
        padding: 16px;
        border: 1px solid var(--border-color);
        min-height: 200px;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        align-items: flex-start;
      }

      .heatmap-cell {
        background: linear-gradient(135deg, #e5e7eb, #f3f4f6);
        border-radius: 8px;
        padding: 12px;
        min-width: 120px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid transparent;
      }

      .heatmap-cell:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        border-color: var(--primary-color);
      }

      .heatmap-cell-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: white;
        margin-bottom: 4px;
      }

      .heatmap-cell-value {
        font-size: 1.125rem;
        font-weight: 700;
        color: white;
      }

      /* Enhanced Dashboard Header Styles */
      .dashboard-header {
        background: linear-gradient(135deg, var(--white) 0%, #f8fafc 100%);
        border-radius: var(--radius-2xl);
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
      }

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 2rem;
      }

      .dashboard-title {
        font-size: 2.25rem;
        font-weight: 800;
        color: var(--text-primary);
        margin: 0 0 0.5rem 0;
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .dashboard-title i {
        color: var(--primary-color);
        font-size: 2rem;
      }

      .dashboard-subtitle {
        font-size: 1.125rem;
        color: var(--text-secondary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .quick-stats {
        display: flex;
        gap: 2rem;
        align-items: center;
      }

      .quick-stat {
        text-align: center;
        padding: 1rem;
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--secondary-color)
        );
        border-radius: var(--radius-lg);
        color: white;
        min-width: 80px;
        box-shadow: var(--shadow-md);
      }

      .quick-stat-value {
        display: block;
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
      }

      .quick-stat-label {
        display: block;
        font-size: 0.75rem;
        opacity: 0.9;
        font-weight: 500;
      }

      /* Filter Panel Styles */
      .filter-panel {
        background: var(--white);
        border-radius: var(--radius-xl);
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
        animation: slideDown 0.3s ease;
      }

      @keyframes slideDown {
        from {
          opacity: 0;
          transform: translateY(-20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .filter-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--border-color);
      }

      .filter-header h3 {
        margin: 0;
        color: var(--text-primary);
        font-size: 1.25rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .filter-close {
        background: none;
        border: none;
        color: var(--text-secondary);
        font-size: 1.25rem;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: var(--radius-md);
        transition: all 0.3s ease;
      }

      .filter-close:hover {
        background: var(--danger-color);
        color: white;
      }

      .filter-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1.5rem;
      }

      .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      }

      .filter-group label {
        font-weight: 600;
        color: var(--text-primary);
        font-size: 0.875rem;
      }

      .filter-group select,
      .filter-group input {
        padding: 0.75rem;
        border: 2px solid var(--border-color);
        border-radius: var(--radius-md);
        background: white;
        color: var(--text-primary);
        font-size: 0.875rem;
        transition: all 0.3s ease;
      }

      .filter-group select:focus,
      .filter-group input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .filter-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
      }
    </style>
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="container">
        <a href="#" class="navbar-brand">
          {% if school_info.logo_url and school_info.logo_url !=
          '/static/images/default_logo.png' %}
          <img
            src="{{ school_info.logo_url }}"
            alt="School Logo"
            style="
              height: 40px;
              width: auto;
              margin-right: 10px;
              border-radius: 8px;
            "
          />
          {% endif %}
          <span
            >{{ school_info.school_name or 'Hillview School Management System'
            }}</span
          >
        </a>
        <ul class="navbar-nav">
          <li>
            <a href="{{ url_for('admin.dashboard') }}" class="nav-link">
              <i class="fas fa-chart-line"></i> Dashboard
            </a>
          </li>
          <li>
            <a href="{{ url_for('universal.dashboard') }}" class="nav-link">
              <i class="fas fa-users-cog"></i> Universal Access
            </a>
          </li>
          <li>
            <a
              href="{{ url_for('subject_config_api.subject_configuration_page') }}"
              class="nav-link"
            >
              <i class="fas fa-cogs"></i> Subject Configuration
            </a>
          </li>
          <li>
            <a
              href="{{ url_for('admin.analytics_dashboard') }}"
              class="nav-link"
            >
              <i class="fas fa-chart-bar"></i> Analytics
            </a>
          </li>
          <li>
            <a href="{{ url_for('admin.reports') }}" class="nav-link">
              <i class="fas fa-file-alt"></i> Reports
            </a>
          </li>
          <li>
            <a href="{{ url_for('admin.manage_teachers') }}" class="nav-link">
              <i class="fas fa-users"></i> Staff
            </a>
          </li>
          <li>
            <a href="{{ url_for('admin.student_promotion') }}" class="nav-link">
              <i class="fas fa-graduation-cap"></i> Student Promotion
            </a>
          </li>
          <li>
            <a
              href="{{ url_for('permission.manage_permissions') }}"
              class="nav-link"
            >
              <i class="fas fa-key"></i> Permissions
            </a>
          </li>
          <li>
            <a href="{{ url_for('auth.logout_route') }}" class="logout-btn">
              <i class="fas fa-sign-out-alt"></i> Logout
            </a>
          </li>
        </ul>
      </div>
    </nav>

    <div class="dashboard-container container">
      <!-- Enhanced Dashboard Header with Quick Actions -->
      <div class="dashboard-header">
        <div class="header-content">
          <div class="header-info">
            <h1 class="dashboard-title">
              <i class="fas fa-tachometer-alt"></i>
              Headteacher Dashboard
            </h1>
            <p class="dashboard-subtitle">
              <i class="fas fa-calendar-day"></i>
              {{ current_academic_year or '2025/2026' }} Academic Year - {{
              current_term or 'Term 1' }}
            </p>
          </div>
          <div class="header-actions">
            <div class="quick-stats">
              <div class="quick-stat">
                <span class="quick-stat-value">{{ total_students }}</span>
                <span class="quick-stat-label">Students</span>
              </div>
              <div class="quick-stat">
                <span class="quick-stat-value">{{ total_teachers }}</span>
                <span class="quick-stat-label">Teachers</span>
              </div>
              <div class="quick-stat">
                <span class="quick-stat-value"
                  >{{ "%.1f"|format(avg_performance) }}%</span
                >
                <span class="quick-stat-label">Avg Performance</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Enhanced Dashboard Actions -->
      <div class="dashboard-actions">
        <button
          class="action-btn action-btn-outline"
          onclick="toggleFilterPanel()"
        >
          <i class="fas fa-filter"></i>
          <span>Advanced Filters</span>
        </button>
        <button
          class="action-btn action-btn-outline"
          onclick="refreshDashboard()"
        >
          <i class="fas fa-sync-alt"></i>
          <span>Refresh Data</span>
        </button>
        <button class="action-btn" onclick="generateComprehensiveReport()">
          <i class="fas fa-file-download"></i>
          <span>Export Report</span>
        </button>
        <button class="action-btn" onclick="openAnalyticsDashboard()">
          <i class="fas fa-chart-line"></i>
          <span>Advanced Analytics</span>
        </button>
      </div>

      <!-- Advanced Filter Panel (Hidden by default) -->
      <div class="filter-panel" id="filterPanel" style="display: none">
        <div class="filter-content">
          <div class="filter-header">
            <h3><i class="fas fa-sliders-h"></i> Advanced Filters</h3>
            <button class="filter-close" onclick="toggleFilterPanel()">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="filter-grid">
            <div class="filter-group">
              <label for="gradeFilter">Grade Level</label>
              <select id="gradeFilter" onchange="applyFilters()">
                <option value="">All Grades</option>
                <option value="1">Grade 1</option>
                <option value="2">Grade 2</option>
                <option value="3">Grade 3</option>
                <option value="4">Grade 4</option>
                <option value="5">Grade 5</option>
                <option value="6">Grade 6</option>
                <option value="7">Grade 7</option>
                <option value="8">Grade 8</option>
              </select>
            </div>
            <div class="filter-group">
              <label for="termFilter">Term</label>
              <select id="termFilter" onchange="applyFilters()">
                <option value="">All Terms</option>
                <option value="1">Term 1</option>
                <option value="2">Term 2</option>
                <option value="3">Term 3</option>
              </select>
            </div>
            <div class="filter-group">
              <label for="performanceFilter">Performance Level</label>
              <select id="performanceFilter" onchange="applyFilters()">
                <option value="">All Levels</option>
                <option value="excellent">Excellent (≥75%)</option>
                <option value="good">Good (50-74%)</option>
                <option value="average">Average (30-49%)</option>
                <option value="below">Below Average (<30%)</option>
              </select>
            </div>
            <div class="filter-group">
              <label for="dateRange">Date Range</label>
              <input type="date" id="dateFrom" onchange="applyFilters()" />
              <input type="date" id="dateTo" onchange="applyFilters()" />
            </div>
          </div>
          <div class="filter-actions">
            <button
              class="action-btn action-btn-outline"
              onclick="resetFilters()"
            >
              <i class="fas fa-undo"></i> Reset
            </button>
            <button class="action-btn" onclick="applyFilters()">
              <i class="fas fa-check"></i> Apply Filters
            </button>
          </div>
        </div>
      </div>

      <!-- Enhanced School Statistics -->
      <div class="section-header">
        <h2>
          <i class="fas fa-chart-pie" style="color: var(--primary-color)"></i>
          School Statistics
        </h2>
      </div>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-label">Total Students</div>
          <div class="stat-value">{{ total_students }}</div>
          <div class="stat-trend">📈 Active learners</div>
        </div>
        <div class="stat-card">
          <div class="stat-label">Total Teachers</div>
          <div class="stat-value">{{ total_teachers }}</div>
          <div class="stat-trend">👨‍🏫 Teaching staff</div>
        </div>
        <div class="stat-card">
          <div class="stat-label">Average Performance</div>
          <div class="stat-value">{{ avg_performance }}%</div>
          <div class="stat-trend">📊 School average</div>
        </div>
        <div class="stat-card">
          <div class="stat-label">Total Classes</div>
          <div class="stat-value">{{ total_classes }}</div>
          <div class="stat-trend">🏫 Active streams</div>
        </div>
        <div class="stat-card">
          <div class="stat-label">Total Subjects</div>
          <div class="stat-value">{{ total_subjects }}</div>
          <div class="stat-trend">📚 Curriculum subjects</div>
        </div>
        <div class="stat-card">
          <div class="stat-label">Total Assessments</div>
          <div class="stat-value">{{ total_assessments }}</div>
          <div class="stat-trend">📝 Completed assessments</div>
        </div>
      </div>

      <!-- Enhanced Performance Highlights -->
      <div class="section-header">
        <h2>
          <i class="fas fa-trophy" style="color: var(--warning-color)"></i>
          Performance Highlights
        </h2>
      </div>
      <div class="highlights-grid">
        <div class="highlight-card">
          <div class="highlight-label">
            <i class="fas fa-medal"></i>
            Top Performing Class
          </div>
          <div class="highlight-value">{{ top_class or 'N/A' }}</div>
          <p>Average score: {{ top_class_score or 0 }}%</p>
        </div>
        <div
          class="highlight-card"
          style="
            background: linear-gradient(
              135deg,
              var(--warning-color) 0%,
              #d69e2e 100%
            );
          "
        >
          <div class="highlight-label">
            <i class="fas fa-exclamation-triangle"></i>
            Needs Attention
          </div>
          <div class="highlight-value">
            {{ least_performing_grade or 'N/A' }}
          </div>
          <p>Average score: {{ least_grade_score or 0 }}%</p>
        </div>
      </div>

      <!-- Enhanced Performance Alerts -->
      {% if performance_alerts %}
      <div class="section-header">
        <h2>
          <i class="fas fa-bell" style="color: var(--danger-color)"></i>
          Performance Alerts
        </h2>
      </div>
      <div class="alerts-container">
        {% for alert in performance_alerts %}
        <div class="alert alert-{{ alert.type }}">
          <div class="alert-header">
            <span class="alert-icon">
              {% if alert.type == 'warning' %}⚠️{% else %}🚨{% endif %}
            </span>
            <strong>{{ alert.title }}</strong>
          </div>
          <div class="alert-message">{{ alert.message }}</div>
          <div class="alert-action">
            <small
              ><strong>Recommended Action:</strong> {{ alert.action }}</small
            >
          </div>
        </div>
        {% endfor %}
      </div>
      {% endif %}

      <!-- Enhanced Academic Calendar Overview -->
      {% if current_term %}
      <div class="section-header">
        <h2>
          <i
            class="fas fa-calendar-alt"
            style="color: var(--success-color)"
          ></i>
          Academic Calendar Overview
        </h2>
      </div>
      <div class="calendar-overview">
        <div class="current-term">
          <div class="term-header">
            <div class="term-badge">
              <i class="fas fa-calendar-check"></i>
              Current Term
            </div>
            <h3 class="term-name">{{ current_term.name }}</h3>
          </div>

          {% if upcoming_assessments %}
          <div class="upcoming-assessments">
            <div class="assessments-header">
              <h4>
                <i class="fas fa-clipboard-list"></i>
                Assessment Types
              </h4>
              <div class="assessments-count">
                {{ upcoming_assessments|length }} Types
              </div>
            </div>
            <div class="assessments-grid">
              {% for assessment in upcoming_assessments %}
              <div class="assessment-card">
                <div class="assessment-icon">
                  {% if assessment.name == 'KJSEA' %}
                  <i class="fas fa-graduation-cap"></i>
                  {% elif 'Mid Term' in assessment.name %}
                  <i class="fas fa-clock"></i>
                  {% elif 'End Term' in assessment.name %}
                  <i class="fas fa-flag-checkered"></i>
                  {% else %}
                  <i class="fas fa-file-alt"></i>
                  {% endif %}
                </div>
                <div class="assessment-details">
                  <div class="assessment-name">{{ assessment.name }}</div>
                  <div class="assessment-weight">
                    <span class="weight-label">Weight:</span>
                    <span class="weight-value">{{ assessment.weight }}%</span>
                  </div>
                </div>
                <div class="assessment-status">
                  <span class="status-badge status-active">Active</span>
                </div>
              </div>
              {% endfor %}
            </div>
          </div>
          {% else %}
          <div class="no-assessments">
            <i class="fas fa-info-circle"></i>
            <p>No assessment types configured for this term.</p>
          </div>
          {% endif %}
        </div>
      </div>
      {% endif %}

      <!-- Enhanced System Alerts -->
      {% if system_alerts %}
      <div class="section-header">
        <h2>
          <i
            class="fas fa-exclamation-triangle"
            style="color: var(--warning-color)"
          ></i>
          System Alerts
        </h2>
        <div class="alerts-summary">
          <span class="alerts-count"
            >{{ system_alerts|length }} Alert{{ 's' if system_alerts|length != 1
            else '' }}</span
          >
        </div>
      </div>
      <div class="system-alerts-container">
        {% for alert in system_alerts %}
        <div class="system-alert alert-{{ alert.type }}">
          <div class="alert-indicator">
            <div class="alert-icon">
              {% if alert.type == 'warning' %}
              <i class="fas fa-exclamation-triangle"></i>
              {% elif alert.type == 'info' %}
              <i class="fas fa-info-circle"></i>
              {% elif alert.type == 'critical' %}
              <i class="fas fa-exclamation-circle"></i>
              {% else %}
              <i class="fas fa-bell"></i>
              {% endif %}
            </div>
            <div class="alert-priority">
              {% if alert.type == 'critical' %}High{% elif alert.type ==
              'warning' %}Medium{% else %}Low{% endif %}
            </div>
          </div>
          <div class="alert-content">
            <div class="alert-header">
              <div class="alert-title">{{ alert.title }}</div>
              <div class="alert-timestamp">
                <i class="fas fa-clock"></i>
                Just now
              </div>
            </div>
            <div class="alert-message">{{ alert.message }}</div>
            {% if alert.action %}
            <div class="alert-action">
              <i class="fas fa-lightbulb"></i>
              <strong>Recommended Action:</strong> {{ alert.action }}
            </div>
            {% endif %}
          </div>
          <div class="alert-actions">
            <button class="alert-btn btn-dismiss" onclick="dismissAlert(this)">
              <i class="fas fa-times"></i>
              Dismiss
            </button>
            <button
              class="alert-btn btn-details"
              onclick="showAlertDetails('{{ alert.title }}')"
            >
              <i class="fas fa-info"></i>
              Details
            </button>
          </div>
        </div>
        {% endfor %}
      </div>
      {% else %}
      <div class="section-header">
        <h2>
          <i
            class="fas fa-check-circle"
            style="color: var(--success-color)"
          ></i>
          System Status
        </h2>
      </div>
      <div class="no-alerts-container">
        <div class="no-alerts-content">
          <i class="fas fa-shield-alt"></i>
          <h3>All Systems Operational</h3>
          <p>
            No system alerts at this time. All systems are running smoothly.
          </p>
        </div>
      </div>
      {% endif %}

      <!-- Performance Distribution -->
      {% if performance_distribution %}
      <div class="section-header">
        <h2>Overall Performance Distribution</h2>
      </div>
      <div class="performance-distribution">
        <div class="distribution-grid">
          <div class="distribution-card grade-ee">
            <div class="distribution-label">E.E (≥75%)</div>
            <div class="distribution-value">
              {{ performance_distribution['E.E'] }}
            </div>
            <div class="distribution-desc">Exceeds Expectations</div>
          </div>
          <div class="distribution-card grade-me">
            <div class="distribution-label">M.E (50-74%)</div>
            <div class="distribution-value">
              {{ performance_distribution['M.E'] }}
            </div>
            <div class="distribution-desc">Meets Expectations</div>
          </div>
          <div class="distribution-card grade-ae">
            <div class="distribution-label">A.E (30-49%)</div>
            <div class="distribution-value">
              {{ performance_distribution['A.E'] }}
            </div>
            <div class="distribution-desc">Approaches Expectations</div>
          </div>
          <div class="distribution-card grade-be">
            <div class="distribution-label">B.E (<30%)</div>
            <div class="distribution-value">
              {{ performance_distribution['B.E'] }}
            </div>
            <div class="distribution-desc">Below Expectations</div>
          </div>
        </div>
      </div>
      {% endif %}

      <!-- Performance Overview Dashboard -->
      <div class="section-header">
        <h2>Performance Overview</h2>
        <div class="chart-controls">
          <button
            class="btn-outline"
            onclick="exportChartsAsPDF()"
            title="Export charts as PDF"
          >
            📊 Export Charts
          </button>
          <button
            class="btn-outline"
            onclick="resetFilters()"
            title="Reset all filters"
          >
            🔄 Reset Filters
          </button>
        </div>
      </div>

      <!-- Key Statistics Cards -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">👥</div>
          <div class="stat-content">
            <div class="stat-number" id="total-assessed">
              {{ total_students }}
            </div>
            <div class="stat-label">Total Students</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">🏆</div>
          <div class="stat-content">
            <div class="stat-number" id="best-class">
              {{ top_class or 'N/A' }}
            </div>
            <div class="stat-label">Best Performing Class</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">📊</div>
          <div class="stat-content">
            <div class="stat-number" id="school-average">
              {{ "%.1f"|format(avg_performance) }}%
            </div>
            <div class="stat-label">School Average</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">📋</div>
          <div class="stat-content">
            <div class="stat-number" id="reports-generated">
              {{ performance_data|length }}
            </div>
            <div class="stat-label">Reports Generated</div>
          </div>
        </div>
      </div>

      <!-- Charts Container -->
      <div class="charts-container">
        <div class="chart-card">
          <h3>Performance Distribution</h3>
          <div class="chart-wrapper">
            <canvas id="performanceDonutChart"></canvas>
          </div>
        </div>

        <div class="chart-card">
          <h3>Class Performance Comparison</h3>
          <div class="chart-wrapper">
            <canvas id="classComparisonChart"></canvas>
          </div>
        </div>
      </div>

      <!-- Advanced Analytics Section -->
      <div class="advanced-analytics">
        <!-- Enhanced Performance Heatmap -->
        <div class="chart-card heatmap-card">
          <div class="chart-header">
            <h3>
              <i class="fas fa-th" style="color: var(--primary-color)"></i>
              Performance Heatmap
            </h3>
            <div class="chart-info">
              <i
                class="fas fa-info-circle"
                title="Visual representation of class performance across different metrics"
              ></i>
            </div>
          </div>
          <div class="heatmap-controls">
            <div class="control-group">
              <label for="heatmapMetric">
                <i class="fas fa-filter"></i>
                Metric:
              </label>
              <select id="heatmapMetric" onchange="updateHeatmap()">
                <option value="average">📊 Class Average</option>
                <option value="excellence">🏆 Excellence Rate (EE1+EE2)</option>
                <option value="mastery">📈 Mastery Rate (ME1+ME2)</option>
              </select>
            </div>
          </div>
          <div class="heatmap-container" id="heatmapContainer">
            <!-- Heatmap will be generated here -->
          </div>
        </div>

        <!-- Performance Alerts -->
        <div class="chart-card alerts-card">
          <h3>Performance Alerts</h3>
          <div class="alerts-container" id="performanceAlerts">
            <!-- Alerts will be generated here -->
          </div>
        </div>

        <!-- Drill-down Analytics -->
        <div class="chart-card drill-down-card">
          <h3>Detailed Analytics</h3>
          <div class="drill-down-container" id="drillDownContainer">
            <div class="drill-down-placeholder">
              Click on any chart element above to see detailed analytics
            </div>
          </div>
        </div>
      </div>

      <!-- Learners per Grade -->
      <div class="section-header">
        <h2>Learners per Grade</h2>
      </div>
      <div class="data-card">
        <div class="card-header">
          <h3 class="card-title">Grade Distribution</h3>
        </div>
        <div class="table-responsive">
          <table class="data-table">
            <thead>
              <tr>
                <th>Grade/Stream</th>
                <th>Total Learners</th>
                <th>Boys</th>
                <th>Girls</th>
                <th>Gender Ratio</th>
              </tr>
            </thead>
            <tbody>
              {% for grade, count in learners_per_grade.items() %}
              <!-- Main row for the grade -->
              <tr class="grade-row">
                <td><strong>{{ grade }}</strong></td>
                <td>{{ count }}</td>
                <td class="gender-male">
                  {{ gender_per_grade[grade]['Male'] }}
                </td>
                <td class="gender-female">
                  {{ gender_per_grade[grade]['Female'] }}
                </td>
                <td>
                  {{ ((gender_per_grade[grade]['Male'] / count) * 100) | round
                  }}% : {{ ((gender_per_grade[grade]['Female'] / count) * 100) |
                  round }}%
                </td>
              </tr>
              <!-- Sub-rows for each stream in this grade -->
              {% for stream_name, stream_data in
              streams_per_grade[grade].items() %}
              <tr class="stream-row">
                <td style="padding-left: 30px">{{ stream_name }}</td>
                <td>{{ stream_data['total'] }}</td>
                <td class="gender-male">{{ stream_data['Male'] }}</td>
                <td class="gender-female">{{ stream_data['Female'] }}</td>
                <td>
                  {% if stream_data['total'] > 0 %} {{ ((stream_data['Male'] /
                  stream_data['total']) * 100) | round }}% : {{
                  ((stream_data['Female'] / stream_data['total']) * 100) | round
                  }}% {% else %} N/A {% endif %}
                </td>
              </tr>
              {% endfor %} {% endfor %}
            </tbody>
          </table>
        </div>
      </div>

      <!-- Performance Overview -->
      <div class="section-header">
        <h2>Detailed Performance Assessment Results</h2>
      </div>
      <div class="data-card">
        <div class="card-header">
          <h3 class="card-title">Assessment Results by Grade and Stream</h3>
          <div class="card-actions">
            <button class="btn-outline" onclick="exportPerformanceData()">
              Export Data
            </button>
            <button class="btn-outline" onclick="toggleFilters()">
              Filters
            </button>
          </div>
        </div>

        <!-- Filter Section -->
        <div
          id="performance-filters"
          class="filter-section"
          style="display: none"
        >
          <div class="filter-grid">
            <select id="grade-filter" onchange="filterPerformanceData()">
              <option value="">All Grades</option>
              {% for grade in learners_per_grade.keys() %}
              <option value="{{ grade }}">{{ grade }}</option>
              {% endfor %}
            </select>
            <select id="term-filter" onchange="filterPerformanceData()">
              <option value="">All Terms</option>
              {% if current_term %}
              <option value="{{ current_term.name }}">
                {{ current_term.name }}
              </option>
              {% endif %}
            </select>
            <select id="assessment-filter" onchange="filterPerformanceData()">
              <option value="">All Assessment Types</option>
              {% for assessment in upcoming_assessments %}
              <option value="{{ assessment.name }}">
                {{ assessment.name }}
              </option>
              {% endfor %}
            </select>
          </div>
        </div>

        <div class="table-responsive">
          <table class="data-table" id="performance-table">
            <thead>
              <tr>
                <th>Grade</th>
                <th>Stream</th>
                <th>Term</th>
                <th>Assessment Type</th>
                <th>Students</th>
                <th>Class Average</th>
                <th>EE1 (≥90%)</th>
                <th>EE2 (75-89%)</th>
                <th>ME1 (58-74%)</th>
                <th>ME2 (41-57%)</th>
                <th>AE1 (31-40%)</th>
                <th>AE2 (21-30%)</th>
                <th>BE1 (11-20%)</th>
                <th>BE2 (<11%)</th>
              </tr>
            </thead>
            <tbody id="performance-table-body">
              {% if performance_data %} {% for data in performance_data %}
              <tr
                class="performance-row"
                data-grade="{{ data.grade }}"
                data-term="{{ data.term }}"
                data-assessment="{{ data.assessment_type }}"
                data-page="1"
              >
                <td><strong>{{ data.grade }}</strong></td>
                <td>{{ data.stream }}</td>
                <td>{{ data.term }}</td>
                <td>{{ data.assessment_type }}</td>
                <td>{{ data.total_students }}</td>
                <td>
                  <strong>{{ "%.2f"|format(data.class_average) }}</strong>
                </td>
                <td class="count-ee1">{{ data.performance_counts['EE1'] }}</td>
                <td class="count-ee2">{{ data.performance_counts['EE2'] }}</td>
                <td class="count-me1">{{ data.performance_counts['ME1'] }}</td>
                <td class="count-me2">{{ data.performance_counts['ME2'] }}</td>
                <td class="count-ae1">{{ data.performance_counts['AE1'] }}</td>
                <td class="count-ae2">{{ data.performance_counts['AE2'] }}</td>
                <td class="count-be1">{{ data.performance_counts['BE1'] }}</td>
                <td class="count-be2">{{ data.performance_counts['BE2'] }}</td>
              </tr>
              {% endfor %} {% else %}
              <tr>
                <td colspan="14" class="text-center">
                  No performance data available. Generate reports to see
                  assessment results.
                </td>
              </tr>
              {% endif %}
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="pagination-container" id="pagination-container">
          <div class="pagination-info" id="pagination-info">
            Showing all results
          </div>
          <div class="pagination-controls">
            <button
              class="btn-outline"
              id="prev-btn"
              onclick="previousPage()"
              disabled
            >
              ← Previous
            </button>
            <div class="page-numbers" id="page-numbers">
              <!-- Page numbers will be inserted here -->
            </div>
            <span class="page-info" id="page-info">Page 1 of 1</span>
            <button
              class="btn-outline"
              id="next-btn"
              onclick="nextPage()"
              disabled
            >
              Next →
            </button>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <footer>
        <p>
          © 2025 {{ school_info.school_name or 'School Management System' }}. {{
          school_info.report_footer or 'Powered by Hillview SMS' }}
        </p>
      </footer>
    </div>

    <!-- Chart.js Library -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- jsPDF for PDF export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <!-- html2canvas for chart capture -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <script>
      // Global variables for pagination and filtering
      let currentPage = 1;
      const itemsPerPage = 10; // Reduced for better pagination visibility
      let allPerformanceData = [];
      let filteredData = [];

      // Global variables for chart filtering
      let performanceChart = null;
      let comparisonChart = null;
      let activeFilters = {
        performanceLevel: null,
        grade: null
      };

      // Initialize performance data from template
      document.addEventListener("DOMContentLoaded", function () {
        // Store all performance data for filtering and pagination
        const rows = document.querySelectorAll(".performance-row");
        allPerformanceData = Array.from(rows).map((row) => ({
          element: row,
          grade: row.dataset.grade,
          term: row.dataset.term,
          assessment: row.dataset.assessment,
        }));
        filteredData = [...allPerformanceData];

        // Initialize pagination
        if (allPerformanceData.length > 0) {
          displayPage();
          updatePaginationInfo();
        } else {
          // Hide pagination if no data
          document.getElementById("pagination-container").style.display =
            "none";
        }

        // Initialize charts and animations
        initializeCharts();
        animateCounters();

        // Initialize advanced analytics
        generatePerformanceHeatmap();
        generatePerformanceAlerts();
        initializeDrillDown();
      });

      // Toggle filter section visibility
      function toggleFilters() {
        const filterSection = document.getElementById("performance-filters");
        if (filterSection.style.display === "none") {
          filterSection.style.display = "block";
        } else {
          filterSection.style.display = "none";
        }
      }

      // Filter performance data based on selected criteria
      function filterPerformanceData() {
        const gradeFilter = document.getElementById("grade-filter").value;
        const termFilter = document.getElementById("term-filter").value;
        const assessmentFilter =
          document.getElementById("assessment-filter").value;

        filteredData = allPerformanceData.filter((item) => {
          return (
            (!gradeFilter || item.grade === gradeFilter) &&
            (!termFilter || item.term === termFilter) &&
            (!assessmentFilter || item.assessment === assessmentFilter)
          );
        });

        currentPage = 1;
        displayPage();
        updatePaginationInfo();
      }

      // Display current page of filtered data
      function displayPage() {
        // Hide all rows first
        allPerformanceData.forEach((item) => {
          item.element.style.display = "none";
        });

        // Show rows for current page
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const pageData = filteredData.slice(startIndex, endIndex);

        pageData.forEach((item) => {
          item.element.style.display = "";
        });

        // Update pagination controls
        updatePaginationControls();
      }

      // Update pagination information
      function updatePaginationInfo() {
        const totalPages = Math.ceil(filteredData.length / itemsPerPage);
        const startItem =
          filteredData.length > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0;
        const endItem = Math.min(
          currentPage * itemsPerPage,
          filteredData.length
        );

        const paginationContainer = document.getElementById(
          "pagination-container"
        );
        const paginationInfo = document.getElementById("pagination-info");
        const pageInfo = document.getElementById("page-info");

        if (filteredData.length > 0) {
          paginationContainer.style.display = "flex";
          if (filteredData.length > itemsPerPage) {
            paginationInfo.textContent = `Showing ${startItem}-${endItem} of ${filteredData.length} results`;
            pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;
            // Generate page numbers
            generatePageNumbers(totalPages);
          } else {
            paginationInfo.textContent = `Showing all ${filteredData.length} results`;
            pageInfo.textContent = `Page 1 of 1`;
            // Clear page numbers for single page
            document.getElementById("page-numbers").innerHTML = "";
          }
        } else {
          paginationContainer.style.display = "none";
        }
      }

      // Generate page number buttons
      function generatePageNumbers(totalPages) {
        const pageNumbers = document.getElementById("page-numbers");
        pageNumbers.innerHTML = "";

        const maxVisiblePages = 5;
        let startPage = Math.max(
          1,
          currentPage - Math.floor(maxVisiblePages / 2)
        );
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
          startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
          const pageBtn = document.createElement("button");
          pageBtn.textContent = i;
          pageBtn.className =
            i === currentPage ? "btn page-btn active" : "btn-outline page-btn";
          pageBtn.onclick = () => goToPage(i);
          pageNumbers.appendChild(pageBtn);
        }
      }

      // Go to specific page
      function goToPage(page) {
        const totalPages = Math.ceil(filteredData.length / itemsPerPage);
        if (page >= 1 && page <= totalPages) {
          currentPage = page;
          displayPage();
          updatePaginationInfo();
        }
      }

      // Update pagination control buttons
      function updatePaginationControls() {
        const totalPages = Math.ceil(filteredData.length / itemsPerPage);
        const prevButton = document.querySelector(
          'button[onclick="previousPage()"]'
        );
        const nextButton = document.querySelector(
          'button[onclick="nextPage()"]'
        );

        if (prevButton) {
          prevButton.disabled = currentPage === 1;
        }
        if (nextButton) {
          nextButton.disabled = currentPage === totalPages || totalPages === 0;
        }
      }

      // Navigate to previous page
      function previousPage() {
        if (currentPage > 1) {
          currentPage--;
          displayPage();
          updatePaginationInfo();
        }
      }

      // Navigate to next page
      function nextPage() {
        const totalPages = Math.ceil(filteredData.length / itemsPerPage);
        if (currentPage < totalPages) {
          currentPage++;
          displayPage();
          updatePaginationInfo();
        }
      }

      // Export performance data to CSV
      function exportPerformanceData() {
        const headers = [
          "Grade",
          "Stream",
          "Term",
          "Assessment Type",
          "Students",
          "Class Average",
          "EE1 Count",
          "EE2 Count",
          "ME1 Count",
          "ME2 Count",
          "AE1 Count",
          "AE2 Count",
          "BE1 Count",
          "BE2 Count",
        ];
        let csvContent = headers.join(",") + "\n";

        filteredData.forEach((item) => {
          const row = item.element;
          const cells = row.querySelectorAll("td");
          const rowData = Array.from(cells).map((cell) => {
            // Clean up the cell content
            let content = cell.textContent.trim();
            // Remove extra whitespace and newlines
            content = content.replace(/\s+/g, " ");
            // Escape commas and quotes
            if (content.includes(",") || content.includes('"')) {
              content = '"' + content.replace(/"/g, '""') + '"';
            }
            return content;
          });
          csvContent += rowData.join(",") + "\n";
        });

        // Create and download the CSV file
        const blob = new Blob([csvContent], {
          type: "text/csv;charset=utf-8;",
        });
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute("download", "performance_assessment_results.csv");
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }

      // Initialize Charts
      function initializeCharts() {
        // Prepare data from performance_data
        const performanceData = {{ performance_data | tojson }};

        // Calculate overall performance distribution
        const performanceDistribution = {
          'EE1': 0, 'EE2': 0, 'ME1': 0, 'ME2': 0,
          'AE1': 0, 'AE2': 0, 'BE1': 0, 'BE2': 0
        };

        const classData = [];

        performanceData.forEach(data => {
          // Aggregate performance counts
          Object.keys(performanceDistribution).forEach(key => {
            performanceDistribution[key] += data.performance_counts[key] || 0;
          });

          // Collect class data for comparison
          classData.push({
            label: `${data.grade} ${data.stream}`,
            average: data.class_average,
            students: data.total_students
          });
        });

        // Create Performance Distribution Donut Chart
        createPerformanceDonutChart(performanceDistribution);

        // Create Class Comparison Bar Chart
        createClassComparisonChart(classData);
      }

      function createPerformanceDonutChart(data) {
        const ctx = document.getElementById('performanceDonutChart').getContext('2d');

        // Destroy existing chart if it exists
        if (performanceChart) {
          performanceChart.destroy();
        }

        performanceChart = new Chart(ctx, {
          type: 'doughnut',
          data: {
            labels: ['EE1 (≥90%)', 'EE2 (75-89%)', 'ME1 (58-74%)', 'ME2 (41-57%)',
                     'AE1 (31-40%)', 'AE2 (21-30%)', 'BE1 (11-20%)', 'BE2 (<11%)'],
            datasets: [{
              data: [data.EE1, data.EE2, data.ME1, data.ME2, data.AE1, data.AE2, data.BE1, data.BE2],
              backgroundColor: [
                '#10B981', '#34D399', '#60A5FA', '#93C5FD',
                '#FBBF24', '#FCD34D', '#F87171', '#FCA5A5'
              ],
              borderWidth: 3,
              borderColor: '#ffffff',
              hoverBorderWidth: 5
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'bottom',
                labels: {
                  padding: 20,
                  usePointStyle: true,
                  font: {
                    size: 11
                  }
                }
              },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                    return `${context.label}: ${context.parsed} students (${percentage}%)`;
                  }
                }
              }
            },
            animation: {
              animateRotate: true,
              duration: 2000
            },
            onClick: (event, elements) => {
              if (elements.length > 0) {
                const elementIndex = elements[0].index;
                const performanceLevels = ['EE1', 'EE2', 'ME1', 'ME2', 'AE1', 'AE2', 'BE1', 'BE2'];
                const selectedLevel = performanceLevels[elementIndex];
                filterByPerformanceLevel(selectedLevel);

                // Show drill-down analytics
                showDrillDownAnalytics('performance', selectedLevel);
              }
            }
          }
        });
      }

      function createClassComparisonChart(data) {
        const ctx = document.getElementById('classComparisonChart').getContext('2d');

        // Destroy existing chart if it exists
        if (comparisonChart) {
          comparisonChart.destroy();
        }

        // Sort by average for better visualization
        data.sort((a, b) => b.average - a.average);

        comparisonChart = new Chart(ctx, {
          type: 'bar',
          data: {
            labels: data.map(item => item.label),
            datasets: [{
              label: 'Class Average',
              data: data.map(item => item.average),
              backgroundColor: 'rgba(102, 126, 234, 0.8)',
              borderColor: 'rgba(102, 126, 234, 1)',
              borderWidth: 2,
              borderRadius: 8,
              borderSkipped: false,
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    const classInfo = data[context.dataIndex];
                    return [
                      `Class Average: ${context.parsed.y.toFixed(2)}`,
                      `Students: ${classInfo.students}`
                    ];
                  }
                }
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                title: {
                  display: true,
                  text: 'Class Average'
                },
                grid: {
                  color: 'rgba(0,0,0,0.1)'
                }
              },
              x: {
                title: {
                  display: true,
                  text: 'Classes'
                },
                grid: {
                  display: false
                }
              }
            },
            animation: {
              duration: 2000,
              easing: 'easeOutQuart'
            },
            onClick: (event, elements) => {
              if (elements.length > 0) {
                const elementIndex = elements[0].index;
                const selectedClass = data[elementIndex].label;
                const grade = selectedClass.split(' ')[0]; // Extract grade from "Grade 7 Y"
                filterByGrade(grade);

                // Show drill-down analytics
                showDrillDownAnalytics('class', selectedClass, data[elementIndex]);
              }
            }
          }
        });
      }

      // Animate counter numbers
      function animateCounters() {
        const counters = document.querySelectorAll('.stat-number');

        counters.forEach(counter => {
          const target = parseFloat(counter.textContent.replace(/[^\d.]/g, ''));
          if (isNaN(target)) return;

          const increment = target / 50;
          let current = 0;
          const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
              current = target;
              clearInterval(timer);
            }

            // Format the number appropriately
            if (counter.id === 'school-average') {
              counter.textContent = current.toFixed(1) + '%';
            } else if (counter.id === 'best-class') {
              // Don't animate text values
              return;
            } else {
              counter.textContent = Math.floor(current);
            }
          }, 40);
        });
      }

      // Filter functions
      function filterByPerformanceLevel(level) {
        activeFilters.performanceLevel = activeFilters.performanceLevel === level ? null : level;
        applyFilters();
        updateFilterIndicators();
      }

      function filterByGrade(grade) {
        activeFilters.grade = activeFilters.grade === grade ? null : grade;
        applyFilters();
        updateFilterIndicators();
      }

      function applyFilters() {
        filteredData = allPerformanceData.filter(item => {
          let matches = true;

          if (activeFilters.grade) {
            matches = matches && item.grade === activeFilters.grade;
          }

          if (activeFilters.performanceLevel) {
            // Check if this class has students in the selected performance level
            matches = matches && item.performance_counts[activeFilters.performanceLevel] > 0;
          }

          return matches;
        });

        currentPage = 1;
        displayPage();
        updatePaginationInfo();
      }

      function resetFilters() {
        activeFilters = {
          performanceLevel: null,
          grade: null
        };
        applyFilters();
        updateFilterIndicators();
      }

      function updateFilterIndicators() {
        const resetBtn = document.querySelector('button[onclick="resetFilters()"]');
        if (activeFilters.performanceLevel || activeFilters.grade) {
          resetBtn.classList.add('filter-active');
        } else {
          resetBtn.classList.remove('filter-active');
        }
      }

      // Export functionality
      async function exportChartsAsPDF() {
        const { jsPDF } = window.jspdf;
        const pdf = new jsPDF('landscape', 'mm', 'a4');

        try {
          // Add title
          pdf.setFontSize(20);
          pdf.text('Performance Overview Report', 20, 20);
          pdf.setFontSize(12);
          pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 30);

          // Capture performance chart
          const performanceCanvas = document.getElementById('performanceDonutChart');
          const performanceImgData = performanceCanvas.toDataURL('image/png');
          pdf.addImage(performanceImgData, 'PNG', 20, 40, 120, 80);

          // Capture comparison chart
          const comparisonCanvas = document.getElementById('classComparisonChart');
          const comparisonImgData = comparisonCanvas.toDataURL('image/png');
          pdf.addImage(comparisonImgData, 'PNG', 150, 40, 120, 80);

          // Add statistics
          pdf.setFontSize(14);
          pdf.text('Key Statistics:', 20, 140);
          pdf.setFontSize(10);

          const totalStudents = document.getElementById('total-assessed').textContent;
          const bestClass = document.getElementById('best-class').textContent;
          const schoolAverage = document.getElementById('school-average').textContent;
          const reportsGenerated = document.getElementById('reports-generated').textContent;

          pdf.text(`Total Students Assessed: ${totalStudents}`, 20, 150);
          pdf.text(`Best Performing Class: ${bestClass}`, 20, 160);
          pdf.text(`School Average: ${schoolAverage}`, 20, 170);
          pdf.text(`Reports Generated: ${reportsGenerated}`, 20, 180);

          // Save the PDF
          pdf.save('performance-overview.pdf');

          // Show success message
          showNotification('Charts exported successfully!', 'success');

        } catch (error) {
          console.error('Export failed:', error);
          showNotification('Export failed. Please try again.', 'error');
        }
      }

      function showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          padding: 15px 20px;
          border-radius: 8px;
          color: white;
          font-weight: 500;
          z-index: 1000;
          animation: slideIn 0.3s ease;
          background: ${type === 'success' ? '#28a745' : '#dc3545'};
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
          notification.remove();
        }, 3000);
      }

      // Advanced Analytics Functions

      // Performance Heatmap
      function generatePerformanceHeatmap() {
        const performanceData = {{ performance_data | tojson }};
        updateHeatmap();
      }

      function updateHeatmap() {
        const metric = document.getElementById('heatmapMetric').value;
        const performanceData = {{ performance_data | tojson }};
        const container = document.getElementById('heatmapContainer');

        container.innerHTML = '';

        performanceData.forEach(data => {
          const cell = document.createElement('div');
          cell.className = 'heatmap-cell';

          let value, color;

          switch(metric) {
            case 'average':
              value = data.class_average.toFixed(1);
              color = getHeatmapColor(data.class_average, 0, 100);
              break;
            case 'excellence':
              const excellenceRate = ((data.performance_counts.EE1 + data.performance_counts.EE2) / data.total_students * 100);
              value = excellenceRate.toFixed(1) + '%';
              color = getHeatmapColor(excellenceRate, 0, 100);
              break;
            case 'mastery':
              const masteryRate = ((data.performance_counts.ME1 + data.performance_counts.ME2) / data.total_students * 100);
              value = masteryRate.toFixed(1) + '%';
              color = getHeatmapColor(masteryRate, 0, 100);
              break;
          }

          cell.style.background = color;
          cell.innerHTML = `
            <div class="heatmap-label">${data.grade} ${data.stream}</div>
            <div class="heatmap-value">${value}</div>
          `;

          cell.onclick = () => {
            showDrillDownAnalytics('heatmap', `${data.grade} ${data.stream}`, data);
          };

          container.appendChild(cell);
        });
      }

      function getHeatmapColor(value, min, max) {
        const ratio = (value - min) / (max - min);

        if (ratio >= 0.8) return 'linear-gradient(135deg, #10B981, #059669)'; // Excellent - Green
        if (ratio >= 0.6) return 'linear-gradient(135deg, #3B82F6, #1D4ED8)'; // Good - Blue
        if (ratio >= 0.4) return 'linear-gradient(135deg, #F59E0B, #D97706)'; // Average - Orange
        return 'linear-gradient(135deg, #EF4444, #DC2626)'; // Below - Red
      }

      // Performance Alerts
      function generatePerformanceAlerts() {
        const performanceData = {{ performance_data | tojson }};
        const alertsContainer = document.getElementById('performanceAlerts');
        const alerts = [];

        performanceData.forEach(data => {
          const classLabel = `${data.grade} ${data.stream}`;

          // Critical: Very low performance
          if (data.class_average < 30) {
            alerts.push({
              type: 'critical',
              icon: '🚨',
              title: 'Critical Performance Alert',
              message: `${classLabel} has a very low class average of ${data.class_average.toFixed(1)}. Immediate intervention required.`
            });
          }

          // Warning: Low excellence rate
          const excellenceRate = (data.performance_counts.EE1 + data.performance_counts.EE2) / data.total_students * 100;
          if (excellenceRate < 20) {
            alerts.push({
              type: 'warning',
              icon: '⚠️',
              title: 'Low Excellence Rate',
              message: `${classLabel} has only ${excellenceRate.toFixed(1)}% of students achieving excellence (EE1/EE2).`
            });
          }

          // Info: High performance
          if (data.class_average > 80) {
            alerts.push({
              type: 'info',
              icon: '🎉',
              title: 'Outstanding Performance',
              message: `${classLabel} is performing exceptionally well with a class average of ${data.class_average.toFixed(1)}.`
            });
          }

          // Warning: Large class with low performance
          if (data.total_students > 25 && data.class_average < 50) {
            alerts.push({
              type: 'warning',
              icon: '👥',
              title: 'Large Class Concern',
              message: `${classLabel} has ${data.total_students} students with below-average performance. Consider additional support.`
            });
          }
        });

        // Sort alerts by priority (critical first)
        alerts.sort((a, b) => {
          const priority = { critical: 3, warning: 2, info: 1 };
          return priority[b.type] - priority[a.type];
        });

        alertsContainer.innerHTML = '';

        if (alerts.length === 0) {
          alertsContainer.innerHTML = `
            <div class="alert-item alert-info">
              <div class="alert-header">
                <span class="alert-icon">✅</span>
                <span class="alert-title">All Clear</span>
              </div>
              <div class="alert-message">No performance alerts at this time. All classes are performing within expected ranges.</div>
            </div>
          `;
        } else {
          alerts.forEach(alert => {
            const alertElement = document.createElement('div');
            alertElement.className = `alert-item alert-${alert.type}`;
            alertElement.innerHTML = `
              <div class="alert-header">
                <span class="alert-icon">${alert.icon}</span>
                <span class="alert-title">${alert.title}</span>
              </div>
              <div class="alert-message">${alert.message}</div>
            `;
            alertsContainer.appendChild(alertElement);
          });
        }
      }

      // Drill-down Analytics
      function initializeDrillDown() {
        // Initialize with placeholder
        const container = document.getElementById('drillDownContainer');
        container.innerHTML = `
          <div class="drill-down-placeholder">
            Click on any chart element above to see detailed analytics
          </div>
        `;
      }

      function showDrillDownAnalytics(type, identifier, data = null) {
        const container = document.getElementById('drillDownContainer');

        let content = '';

        if (type === 'performance') {
          content = generatePerformanceDrillDown(identifier);
        } else if (type === 'class' || type === 'heatmap') {
          content = generateClassDrillDown(identifier, data);
        }

        container.innerHTML = `
          <div class="drill-down-content">
            <div class="drill-down-header">
              <div class="drill-down-title">Detailed Analytics: ${identifier}</div>
              <button class="drill-down-close" onclick="closeDrillDown()">✕ Close</button>
            </div>
            ${content}
          </div>
        `;
      }

      function generatePerformanceDrillDown(performanceLevel) {
        const performanceData = {{ performance_data | tojson }};

        // Calculate statistics for this performance level
        let totalStudents = 0;
        let classesWithStudents = 0;
        let bestClass = '';
        let bestCount = 0;

        performanceData.forEach(data => {
          const count = data.performance_counts[performanceLevel] || 0;
          totalStudents += count;
          if (count > 0) {
            classesWithStudents++;
            if (count > bestCount) {
              bestCount = count;
              bestClass = `${data.grade} ${data.stream}`;
            }
          }
        });

        const performanceLabels = {
          'EE1': 'Exceeds Expectations 1 (≥90%)',
          'EE2': 'Exceeds Expectations 2 (75-89%)',
          'ME1': 'Meets Expectations 1 (58-74%)',
          'ME2': 'Meets Expectations 2 (41-57%)',
          'AE1': 'Approaches Expectations 1 (31-40%)',
          'AE2': 'Approaches Expectations 2 (21-30%)',
          'BE1': 'Below Expectations 1 (11-20%)',
          'BE2': 'Below Expectations 2 (<11%)'
        };

        return `
          <div class="drill-down-stats">
            <div class="drill-stat">
              <div class="drill-stat-value">${totalStudents}</div>
              <div class="drill-stat-label">Total Students</div>
            </div>
            <div class="drill-stat">
              <div class="drill-stat-value">${classesWithStudents}</div>
              <div class="drill-stat-label">Classes Affected</div>
            </div>
            <div class="drill-stat">
              <div class="drill-stat-value">${bestClass || 'N/A'}</div>
              <div class="drill-stat-label">Highest Count</div>
            </div>
            <div class="drill-stat">
              <div class="drill-stat-value">${bestCount}</div>
              <div class="drill-stat-label">Students in Best Class</div>
            </div>
          </div>
          <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 15px;">
            <h4 style="margin: 0 0 10px 0; color: #333;">Performance Level: ${performanceLabels[performanceLevel]}</h4>
            <p style="margin: 0; color: #666; line-height: 1.5;">
              This performance level represents students who ${getPerformanceDescription(performanceLevel)}.
              Focus on ${getRecommendation(performanceLevel)}.
            </p>
          </div>
        `;
      }

      function generateClassDrillDown(classLabel, data) {
        if (!data) {
          // Find data for this class
          const performanceData = {{ performance_data | tojson }};
          data = performanceData.find(item => `${item.grade} ${item.stream}` === classLabel);
        }

        if (!data) {
          return '<p>No data available for this class.</p>';
        }

        const excellenceRate = ((data.performance_counts.EE1 + data.performance_counts.EE2) / data.total_students * 100).toFixed(1);
        const masteryRate = ((data.performance_counts.ME1 + data.performance_counts.ME2) / data.total_students * 100).toFixed(1);
        const needsSupportRate = ((data.performance_counts.AE1 + data.performance_counts.AE2 + data.performance_counts.BE1 + data.performance_counts.BE2) / data.total_students * 100).toFixed(1);

        return `
          <div class="drill-down-stats">
            <div class="drill-stat">
              <div class="drill-stat-value">${data.total_students}</div>
              <div class="drill-stat-label">Total Students</div>
            </div>
            <div class="drill-stat">
              <div class="drill-stat-value">${data.class_average.toFixed(1)}</div>
              <div class="drill-stat-label">Class Average</div>
            </div>
            <div class="drill-stat">
              <div class="drill-stat-value">${excellenceRate}%</div>
              <div class="drill-stat-label">Excellence Rate</div>
            </div>
            <div class="drill-stat">
              <div class="drill-stat-value">${masteryRate}%</div>
              <div class="drill-stat-label">Mastery Rate</div>
            </div>
            <div class="drill-stat">
              <div class="drill-stat-value">${needsSupportRate}%</div>
              <div class="drill-stat-label">Needs Support</div>
            </div>
          </div>
          <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 15px;">
            <h4 style="margin: 0 0 15px 0; color: #333;">Performance Breakdown</h4>
            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; font-size: 12px;">
              <div style="text-align: center; padding: 8px; background: #10B981; color: white; border-radius: 4px;">
                <div style="font-weight: bold;">${data.performance_counts.EE1}</div>
                <div>EE1 (≥90%)</div>
              </div>
              <div style="text-align: center; padding: 8px; background: #34D399; color: white; border-radius: 4px;">
                <div style="font-weight: bold;">${data.performance_counts.EE2}</div>
                <div>EE2 (75-89%)</div>
              </div>
              <div style="text-align: center; padding: 8px; background: #60A5FA; color: white; border-radius: 4px;">
                <div style="font-weight: bold;">${data.performance_counts.ME1}</div>
                <div>ME1 (58-74%)</div>
              </div>
              <div style="text-align: center; padding: 8px; background: #93C5FD; color: white; border-radius: 4px;">
                <div style="font-weight: bold;">${data.performance_counts.ME2}</div>
                <div>ME2 (41-57%)</div>
              </div>
            </div>
          </div>
        `;
      }

      function getPerformanceDescription(level) {
        const descriptions = {
          'EE1': 'demonstrate exceptional mastery of learning objectives',
          'EE2': 'show strong understanding and application of concepts',
          'ME1': 'meet most learning expectations with good comprehension',
          'ME2': 'meet basic learning expectations with adequate understanding',
          'AE1': 'are approaching expectations but need additional support',
          'AE2': 'require significant support to meet learning objectives',
          'BE1': 'need intensive intervention and remedial support',
          'BE2': 'require immediate and comprehensive intervention'
        };
        return descriptions[level] || 'are in this performance category';
      }

      function getRecommendation(level) {
        const recommendations = {
          'EE1': 'providing enrichment and advanced challenges',
          'EE2': 'maintaining high standards and offering extension activities',
          'ME1': 'reinforcing concepts and providing practice opportunities',
          'ME2': 'additional practice and targeted support',
          'AE1': 'intensive support and differentiated instruction',
          'AE2': 'remedial intervention and individualized learning plans',
          'BE1': 'immediate intervention and specialized support programs',
          'BE2': 'comprehensive intervention and possible assessment for learning difficulties'
        };
        return recommendations[level] || 'appropriate support strategies';
      }

      function closeDrillDown() {
        initializeDrillDown();
      }

      // Enhanced Alert Functions
      function dismissAlert(button) {
        const alertElement = button.closest('.system-alert');
        alertElement.style.transform = 'translateX(100%)';
        alertElement.style.opacity = '0';
        setTimeout(() => {
          alertElement.remove();
          // Update alert count if needed
          updateAlertCount();
        }, 300);
      }

      function showAlertDetails(alertTitle) {
        alert(`Showing details for: ${alertTitle}\n\nThis feature will be enhanced with a modal dialog in future updates.`);
      }

      function updateAlertCount() {
        const remainingAlerts = document.querySelectorAll('.system-alert').length;
        const alertsCount = document.querySelector('.alerts-count');
        if (alertsCount) {
          alertsCount.textContent = `${remainingAlerts} Alert${remainingAlerts !== 1 ? 's' : ''}`;

          // Hide section if no alerts remain
          if (remainingAlerts === 0) {
            const alertsSection = document.querySelector('.system-alerts-container').closest('div');
            alertsSection.innerHTML = `
              <div class="section-header">
                <h2>
                  <i class="fas fa-check-circle" style="color: var(--success-color);"></i>
                  System Status
                </h2>
              </div>
              <div class="no-alerts-container">
                <div class="no-alerts-content">
                  <i class="fas fa-shield-alt"></i>
                  <h3>All Systems Operational</h3>
                  <p>No system alerts at this time. All systems are running smoothly.</p>
                </div>
              </div>
            `;
          }
        }
      }

      // Enhanced Dashboard Functions
      function toggleFilterPanel() {
        const panel = document.getElementById('filterPanel');
        if (panel.style.display === 'none' || panel.style.display === '') {
          panel.style.display = 'block';
          panel.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        } else {
          panel.style.display = 'none';
        }
      }

      function refreshDashboard() {
        // Show loading indicator
        showNotification('Refreshing dashboard data...', 'info');

        // Simulate refresh (in real implementation, this would reload data)
        setTimeout(() => {
          location.reload();
        }, 1000);
      }

      function generateComprehensiveReport() {
        showNotification('Generating comprehensive school report...', 'info');

        // In real implementation, this would trigger report generation
        setTimeout(() => {
          showNotification('Report generation started. You will be notified when ready.', 'success');
        }, 1500);
      }

      function openAnalyticsDashboard() {
        // Navigate to analytics dashboard
        window.location.href = "{{ url_for('admin.analytics_dashboard') }}";
      }

      function applyFilters() {
        const grade = document.getElementById('gradeFilter').value;
        const term = document.getElementById('termFilter').value;
        const performance = document.getElementById('performanceFilter').value;
        const dateFrom = document.getElementById('dateFrom').value;
        const dateTo = document.getElementById('dateTo').value;

        // Show loading
        showNotification('Applying filters...', 'info');

        // In real implementation, this would filter the dashboard data
        console.log('Filters applied:', { grade, term, performance, dateFrom, dateTo });

        setTimeout(() => {
          showNotification('Filters applied successfully!', 'success');
        }, 1000);
      }

      function resetFilters() {
        document.getElementById('gradeFilter').value = '';
        document.getElementById('termFilter').value = '';
        document.getElementById('performanceFilter').value = '';
        document.getElementById('dateFrom').value = '';
        document.getElementById('dateTo').value = '';

        showNotification('Filters reset', 'info');
      }

      function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
          <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
          </div>
          <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
          </button>
        `;

        // Add styles
        notification.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: ${type === 'success' ? '#10B981' : type === 'error' ? '#EF4444' : '#3B82F6'};
          color: white;
          padding: 1rem 1.5rem;
          border-radius: 0.5rem;
          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
          z-index: 9999;
          display: flex;
          align-items: center;
          gap: 1rem;
          max-width: 400px;
          animation: slideInRight 0.3s ease;
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
          if (notification.parentElement) {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => notification.remove(), 300);
          }
        }, 5000);
      }

      // Add notification animations
      const style = document.createElement('style');
      style.textContent = `
        @keyframes slideInRight {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }

        @keyframes slideOutRight {
          from {
            transform: translateX(0);
            opacity: 1;
          }
          to {
            transform: translateX(100%);
            opacity: 0;
          }
        }

        .notification-content {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          flex: 1;
        }

        .notification-close {
          background: none;
          border: none;
          color: white;
          cursor: pointer;
          padding: 0.25rem;
          border-radius: 0.25rem;
          transition: background 0.2s ease;
        }

        .notification-close:hover {
          background: rgba(255, 255, 255, 0.2);
        }
      `;
      document.head.appendChild(style);

      // Add smooth scrolling for better UX
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute("href"));
          if (target) {
            target.scrollIntoView({
              behavior: "smooth",
            });
          }
        });
      });



      // Initialize dashboard on load
      document.addEventListener('DOMContentLoaded', function() {
        // Show welcome message
        setTimeout(() => {
          showNotification('Welcome to the Enhanced Headteacher Dashboard!', 'success');
        }, 1000);
      });
    </script>
  </body>
</html>
