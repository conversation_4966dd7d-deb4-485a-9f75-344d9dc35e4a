/* 
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SCHOOL MANAGEMENT SYSTEM
 * COMPREHENSIVE RESPONSIVE DESIGN FRAMEWORK 2025
 * 
 * This framework ensures 100% mobile responsiveness across all device sizes
 * following modern web standards and best practices.
 */

/* ========================================
   CSS CUSTOM PROPERTIES (CSS VARIABLES)
   ======================================== */

:root {
  /* 2025 STANDARD BREAKPOINTS */
  --breakpoint-xs: 320px; /* Extra Small Mobile (Portrait) */
  --breakpoint-sm: 480px; /* Small Mobile (Landscape) */
  --breakpoint-md: 768px; /* Tablet (Portrait) */
  --breakpoint-lg: 1024px; /* Tablet (Landscape) / Small Desktop */
  --breakpoint-xl: 1280px; /* Desktop */
  --breakpoint-xxl: 1440px; /* Large Desktop */
  --breakpoint-ultra: 1920px; /* Ultra-wide screens */

  /* RESPONSIVE SPACING SYSTEM */
  --space-xs: 0.25rem; /* 4px */
  --space-sm: 0.5rem; /* 8px */
  --space-md: 1rem; /* 16px */
  --space-lg: 1.5rem; /* 24px */
  --space-xl: 2rem; /* 32px */
  --space-2xl: 3rem; /* 48px */
  --space-3xl: 4rem; /* 64px */

  /* RESPONSIVE TYPOGRAPHY SCALE */
  --text-xs: 0.75rem; /* 12px */
  --text-sm: 0.875rem; /* 14px */
  --text-base: 1rem; /* 16px */
  --text-lg: 1.125rem; /* 18px */
  --text-xl: 1.25rem; /* 20px */
  --text-2xl: 1.5rem; /* 24px */
  --text-3xl: 1.875rem; /* 30px */
  --text-4xl: 2.25rem; /* 36px */
  --text-5xl: 3rem; /* 48px */

  /* RESPONSIVE BORDER RADIUS */
  --radius-sm: 0.25rem; /* 4px */
  --radius-md: 0.5rem; /* 8px */
  --radius-lg: 0.75rem; /* 12px */
  --radius-xl: 1rem; /* 16px */
  --radius-2xl: 1.5rem; /* 24px */
  --radius-full: 9999px; /* Fully rounded */

  /* RESPONSIVE SHADOWS */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* TOUCH TARGET SIZES (WCAG 2.1 AA Compliance) */
  --touch-target-min: 44px; /* Minimum touch target size */
  --touch-target-comfortable: 48px; /* Comfortable touch target size */

  /* Z-INDEX SCALE */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ========================================
   RESPONSIVE UTILITY CLASSES
   ======================================== */

/* CONTAINER SYSTEM */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-md);
  padding-right: var(--space-md);
}

.container-fluid {
  width: 100%;
  padding-left: var(--space-md);
  padding-right: var(--space-md);
}

/* RESPONSIVE GRID SYSTEM */
.grid {
  display: grid;
  gap: var(--space-md);
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}
.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}

/* RESPONSIVE FLEXBOX UTILITIES */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.flex-row {
  flex-direction: row;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-nowrap {
  flex-wrap: nowrap;
}

.justify-start {
  justify-content: flex-start;
}
.justify-center {
  justify-content: center;
}
.justify-end {
  justify-content: flex-end;
}
.justify-between {
  justify-content: space-between;
}
.justify-around {
  justify-content: space-around;
}

.items-start {
  align-items: flex-start;
}
.items-center {
  align-items: center;
}
.items-end {
  align-items: flex-end;
}
.items-stretch {
  align-items: stretch;
}

/* RESPONSIVE SPACING UTILITIES */
.p-0 {
  padding: 0;
}
.p-xs {
  padding: var(--space-xs);
}
.p-sm {
  padding: var(--space-sm);
}
.p-md {
  padding: var(--space-md);
}
.p-lg {
  padding: var(--space-lg);
}
.p-xl {
  padding: var(--space-xl);
}

.m-0 {
  margin: 0;
}
.m-xs {
  margin: var(--space-xs);
}
.m-sm {
  margin: var(--space-sm);
}
.m-md {
  margin: var(--space-md);
}
.m-lg {
  margin: var(--space-lg);
}
.m-xl {
  margin: var(--space-xl);
}

/* RESPONSIVE TEXT UTILITIES */
.text-xs {
  font-size: var(--text-xs);
}
.text-sm {
  font-size: var(--text-sm);
}
.text-base {
  font-size: var(--text-base);
}
.text-lg {
  font-size: var(--text-lg);
}
.text-xl {
  font-size: var(--text-xl);
}
.text-2xl {
  font-size: var(--text-2xl);
}

.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}

/* RESPONSIVE DISPLAY UTILITIES */
.hidden {
  display: none;
}
.block {
  display: block;
}
.inline {
  display: inline;
}
.inline-block {
  display: inline-block;
}

/* RESPONSIVE VISIBILITY UTILITIES */
.show-xs {
  display: none;
}
.show-sm {
  display: none;
}
.show-md {
  display: none;
}
.show-lg {
  display: none;
}
.show-xl {
  display: none;
}

.hide-xs {
  display: block;
}
.hide-sm {
  display: block;
}
.hide-md {
  display: block;
}
.hide-lg {
  display: block;
}
.hide-xl {
  display: block;
}

/* ========================================
   MOBILE-FIRST MEDIA QUERIES
   ======================================== */

/* Extra Small Mobile Devices (320px - 479px) */
@media (max-width: 479px) {
  .container {
    padding-left: var(--space-sm);
    padding-right: var(--space-sm);
  }

  .show-xs {
    display: block;
  }
  .hide-xs {
    display: none;
  }

  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-5,
  .grid-cols-6 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .text-responsive {
    font-size: var(--text-sm);
  }
}

/* Small Mobile Devices (480px - 767px) */
@media (min-width: 480px) and (max-width: 767px) {
  .show-sm {
    display: block;
  }
  .hide-sm {
    display: none;
  }

  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-5,
  .grid-cols-6 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* Tablet Portrait (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .show-md {
    display: block;
  }
  .hide-md {
    display: none;
  }

  .grid-cols-4,
  .grid-cols-5,
  .grid-cols-6 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

/* Desktop and Tablet Landscape (1024px - 1279px) */
@media (min-width: 1024px) and (max-width: 1279px) {
  .show-lg {
    display: block;
  }
  .hide-lg {
    display: none;
  }

  .container {
    max-width: 1024px;
  }
}

/* Large Desktop (1280px+) */
@media (min-width: 1280px) {
  .show-xl {
    display: block;
  }
  .hide-xl {
    display: none;
  }

  .container {
    max-width: 1280px;
  }
}

/* ========================================
   TOUCH-FRIENDLY COMPONENTS
   ======================================== */

.touch-target {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  display: flex;
  align-items: center;
  justify-content: center;
}

.touch-target-comfortable {
  min-height: var(--touch-target-comfortable);
  min-width: var(--touch-target-comfortable);
}

/* Remove tap highlight on mobile */
* {
  -webkit-tap-highlight-color: transparent;
}

/* Smooth scrolling for mobile */
html {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* ========================================
   RESPONSIVE TABLES
   ======================================== */

.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

@media (max-width: 767px) {
  .table-mobile-stack {
    display: block;
  }

  .table-mobile-stack thead {
    display: none;
  }

  .table-mobile-stack tbody,
  .table-mobile-stack tr,
  .table-mobile-stack td {
    display: block;
    width: 100%;
  }

  .table-mobile-stack tr {
    border: 1px solid #ddd;
    margin-bottom: var(--space-md);
    border-radius: var(--radius-md);
    padding: var(--space-md);
    background: white;
    box-shadow: var(--shadow-sm);
  }

  .table-mobile-stack td {
    border: none;
    padding: var(--space-xs) 0;
    position: relative;
    padding-left: 30%;
  }

  .table-mobile-stack td:before {
    content: attr(data-label) ": ";
    position: absolute;
    left: 0;
    width: 25%;
    font-weight: bold;
    color: #333;
  }
}

/* ========================================
   RESPONSIVE NAVIGATION
   ======================================== */

.navbar-responsive {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-md);
  background: white;
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-weight: 600;
  font-size: var(--text-lg);
  color: #333;
  text-decoration: none;
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-sm) var(--space-md);
  color: #666;
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  min-height: var(--touch-target-min);
}

.nav-link:hover,
.nav-link.active {
  background: #f8f9fa;
  color: #333;
}

.mobile-nav-toggle {
  display: none;
  background: none;
  border: none;
  font-size: var(--text-xl);
  color: #333;
  cursor: pointer;
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  align-items: center;
  justify-content: center;
}

.mobile-nav-toggle:hover {
  background: #f8f9fa;
}

@media (max-width: 767px) {
  .mobile-nav-toggle {
    display: flex;
  }

  .navbar-nav {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    flex-direction: column;
    gap: 0;
    box-shadow: var(--shadow-lg);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    padding: var(--space-md);
    display: none;
  }

  .navbar-nav.show {
    display: flex;
  }

  .nav-link {
    width: 100%;
    justify-content: flex-start;
    padding: var(--space-md);
    border-radius: var(--radius-md);
  }
}

/* ========================================
   RESPONSIVE FORMS
   ======================================== */

.form-responsive {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
  max-width: 100%;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.form-label {
  font-weight: 500;
  color: #333;
  font-size: var(--text-sm);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-md);
  border: 1px solid #ddd;
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  line-height: 1.5;
  transition: all 0.2s ease;
  min-height: var(--touch-target-min);
  background: white;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
}

.form-row {
  display: flex;
  gap: var(--space-md);
  align-items: flex-end;
}

.form-actions {
  display: flex;
  gap: var(--space-md);
  justify-content: flex-end;
  margin-top: var(--space-xl);
}

@media (max-width: 767px) {
  .form-row {
    flex-direction: column;
    gap: var(--space-lg);
  }

  .form-actions {
    flex-direction: column;
    gap: var(--space-md);
  }

  .form-actions .btn {
    width: 100%;
  }
}

/* ========================================
   RESPONSIVE BUTTONS
   ======================================== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-lg);
  font-size: var(--text-base);
  font-weight: 500;
  line-height: 1;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s ease;
  min-height: var(--touch-target-min);
  white-space: nowrap;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #1e7e34;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
}

.btn-outline {
  background: transparent;
  border: 1px solid currentColor;
}

.btn-sm {
  padding: var(--space-sm) var(--space-md);
  font-size: var(--text-sm);
  min-height: 36px;
}

.btn-lg {
  padding: var(--space-lg) var(--space-xl);
  font-size: var(--text-lg);
  min-height: 56px;
}

@media (max-width: 767px) {
  .btn-mobile-full {
    width: 100%;
  }

  .btn-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
  }

  .btn-group .btn {
    width: 100%;
  }
}

/* ========================================
   RESPONSIVE CARDS
   ======================================== */

.card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--space-lg);
  border-bottom: 1px solid #f0f0f0;
  background: #f8f9fa;
}

.card-title {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: #333;
}

.card-body {
  padding: var(--space-lg);
}

.card-footer {
  padding: var(--space-lg);
  border-top: 1px solid #f0f0f0;
  background: #f8f9fa;
}

@media (max-width: 767px) {
  .card-header,
  .card-body,
  .card-footer {
    padding: var(--space-md);
  }

  .card-title {
    font-size: var(--text-base);
  }
}

/* ========================================
   RESPONSIVE MODALS
   ======================================== */

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-dialog {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.modal.show .modal-dialog {
  transform: scale(1);
}

.modal-header {
  padding: var(--space-lg);
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--text-xl);
  cursor: pointer;
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: #f8f9fa;
}

.modal-body {
  padding: var(--space-lg);
}

.modal-footer {
  padding: var(--space-lg);
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: var(--space-md);
  justify-content: flex-end;
}

@media (max-width: 767px) {
  .modal-dialog {
    width: 95%;
    margin: var(--space-md);
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: var(--space-md);
  }

  .modal-footer {
    flex-direction: column;
  }

  .modal-footer .btn {
    width: 100%;
  }
}

/* ========================================
   RESPONSIVE ALERTS & NOTIFICATIONS
   ======================================== */

.alert {
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-lg);
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
}

.alert-success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.alert-warning {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.alert-info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.alert-close {
  background: none;
  border: none;
  font-size: var(--text-lg);
  cursor: pointer;
  padding: 0;
  margin-left: auto;
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
}

.alert-close:hover {
  background: rgba(0, 0, 0, 0.1);
}

@media (max-width: 767px) {
  .alert {
    padding: var(--space-md);
    flex-direction: column;
    text-align: center;
  }

  .alert-close {
    align-self: flex-end;
    margin: 0;
  }
}

/* ========================================
   PERFORMANCE OPTIMIZATIONS
   ======================================== */

/* Hardware acceleration for smooth animations */
.card,
.btn,
.modal-dialog,
.navbar-nav {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

/* Optimize scrolling on mobile */
.table-responsive,
.modal-body {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Reduce paint operations */
.btn:hover,
.card:hover,
.nav-link:hover {
  will-change: transform, box-shadow;
}

/* ========================================
   ACCESSIBILITY ENHANCEMENTS
   ======================================== */

/* Focus indicators */
.btn:focus,
.form-input:focus,
.form-select:focus,
.form-textarea:focus,
.nav-link:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn,
  .card,
  .form-input,
  .form-select,
  .form-textarea {
    border: 2px solid;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ========================================
   PRINT STYLES
   ======================================== */

@media print {
  .mobile-nav-toggle,
  .navbar-nav,
  .btn,
  .alert {
    display: none !important;
  }

  .card {
    box-shadow: none;
    border: 1px solid #ddd;
  }

  .table-responsive {
    overflow: visible;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  .container {
    max-width: none;
    padding: 0;
  }
}

/* ========================================
   UTILITY CLASSES FOR COMMON PATTERNS
   ======================================== */

/* Loading states */
.loading {
  position: relative;
  pointer-events: none;
  opacity: 0.6;
}

.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Truncate text */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Aspect ratios */
.aspect-square {
  aspect-ratio: 1 / 1;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

/* Sticky positioning */
.sticky-top {
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

/* Full height utilities */
.min-h-screen {
  min-height: 100vh;
}

.h-full {
  height: 100%;
}

/* Overflow utilities */
.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.overflow-x-auto {
  overflow-x: auto;
  overflow-y: hidden;
}

.overflow-y-auto {
  overflow-y: auto;
  overflow-x: hidden;
}

/* Position utilities */
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

/* ========================================
   RESPONSIVE IMAGE UTILITIES
   ======================================== */

.img-responsive {
  max-width: 100%;
  height: auto;
  display: block;
}

.img-cover {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.img-contain {
  object-fit: contain;
  width: 100%;
  height: 100%;
}

/* ========================================
   END OF RESPONSIVE FRAMEWORK
   ======================================== */
