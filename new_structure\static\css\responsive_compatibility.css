/* 
 * RESPONSIVE FRAMEWORK COMPATIBILITY FIXES
 * Fixes layout conflicts between responsive framework and existing CSS
 */

/* ===== GLOBAL COMPATIBILITY FIXES ===== */

/* Prevent aggressive CSS resets from breaking existing layouts */
.responsive-safe * {
    box-sizing: border-box;
}

/* Non-aggressive body styles */
body.responsive-enabled {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== CONTAINER FIXES ===== */

/* Fix container positioning conflicts */
.responsive-container {
    position: relative !important;
    z-index: 1 !important;
    width: 100% !important;
    max-width: none !important;
}

/* Fix navbar positioning */
.responsive-navbar {
    position: relative !important;
    z-index: 1000 !important;
    width: 100% !important;
}

/* Fix content area positioning */
.responsive-content {
    position: relative !important;
    z-index: 1 !important;
    margin-top: 0 !important;
    padding-top: 20px !important;
    width: 100% !important;
}

/* ===== BACKGROUND FIXES ===== */

/* Prevent background conflicts */
body.responsive-body {
    min-height: 100vh !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Fix container overlaps */
.container, 
.main-container,
.dashboard-container {
    position: relative !important;
    z-index: 2 !important;
}

/* ===== LAYOUT FIXES ===== */

/* Fix flexbox conflicts */
.responsive-flex {
    display: flex !important;
    flex-wrap: wrap !important;
}

.responsive-grid {
    display: grid !important;
    gap: 1rem !important;
}

/* ===== MOBILE NAVIGATION FIXES ===== */

/* Mobile menu toggle */
.mobile-menu-toggle {
    display: none !important;
    background: none !important;
    border: none !important;
    font-size: 1.5rem !important;
    cursor: pointer !important;
    padding: 0.5rem !important;
    color: inherit !important;
}

/* Mobile menu */
.mobile-menu {
    display: none !important;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 0 0 1rem 1rem !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
    z-index: 1001 !important;
}

.mobile-menu.active {
    display: block !important;
}

/* ===== RESPONSIVE UTILITIES ===== */

/* Hide on mobile */
@media (max-width: 767px) {
    .hide-mobile {
        display: none !important;
    }
    
    .mobile-menu-toggle {
        display: block !important;
    }
    
    /* Fix table responsiveness */
    .responsive-table {
        display: block !important;
        overflow-x: auto !important;
        white-space: nowrap !important;
    }
    
    /* Stack elements on mobile */
    .responsive-stack {
        flex-direction: column !important;
    }
    
    /* Full width on mobile */
    .responsive-full-mobile {
        width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
}

/* Hide on tablet */
@media (min-width: 768px) and (max-width: 1023px) {
    .hide-tablet {
        display: none !important;
    }
}

/* Hide on desktop */
@media (min-width: 1024px) {
    .hide-desktop {
        display: none !important;
    }
    
    .mobile-menu-toggle {
        display: none !important;
    }
    
    .mobile-menu {
        display: none !important;
    }
}

/* ===== FORM FIXES ===== */

/* Responsive form elements */
.responsive-form input,
.responsive-form select,
.responsive-form textarea {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
}

/* ===== BUTTON FIXES ===== */

/* Touch-friendly buttons */
.responsive-btn {
    min-height: 44px !important;
    min-width: 44px !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 0.5rem !important;
    border: none !important;
    cursor: pointer !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
}

.responsive-btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.responsive-btn:active {
    transform: translateY(0) !important;
}

/* ===== CARD FIXES ===== */

/* Responsive cards */
.responsive-card {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 1rem !important;
    padding: 1.5rem !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    margin-bottom: 1rem !important;
}

/* ===== ANIMATION FIXES ===== */

/* Smooth transitions */
.responsive-transition {
    transition: all 0.3s ease !important;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    .responsive-transition,
    .responsive-btn {
        transition: none !important;
    }
}

/* ===== PRINT FIXES ===== */

@media print {
    .responsive-navbar,
    .mobile-menu-toggle,
    .mobile-menu {
        display: none !important;
    }
    
    .responsive-content {
        margin-top: 0 !important;
        padding-top: 0 !important;
    }
}

/* ===== HIGH CONTRAST MODE ===== */

@media (prefers-contrast: high) {
    .responsive-card {
        border: 2px solid #000 !important;
        background: #fff !important;
    }
    
    .responsive-btn {
        border: 2px solid #000 !important;
    }
}

/* ===== FOCUS INDICATORS ===== */

/* Keyboard navigation support */
.responsive-btn:focus,
.responsive-form input:focus,
.responsive-form select:focus,
.responsive-form textarea:focus {
    outline: 2px solid #007bff !important;
    outline-offset: 2px !important;
}

/* ===== COMPATIBILITY OVERRIDES ===== */

/* Override conflicting styles */
.responsive-override {
    position: relative !important;
    z-index: auto !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Safe positioning */
.responsive-safe-position {
    position: static !important;
    z-index: auto !important;
}

/* Clear floats */
.responsive-clearfix::after {
    content: "" !important;
    display: table !important;
    clear: both !important;
}
