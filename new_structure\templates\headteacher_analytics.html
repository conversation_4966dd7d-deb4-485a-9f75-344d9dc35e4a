{% extends "responsive_base.html" %} {% block title %}Academic Performance
Analytics - Head Teacher{% endblock %} {% block meta_description %}School-Wide
Academic Analytics - Comprehensive performance insights and data-driven decision
making for {{ school_info.school_name or 'Hillview School' }}{% endblock %} {%
block page_title %}School-Wide Academic Analytics{% endblock %} {% block
page_subtitle %}
<p class="text-lg">
  Comprehensive performance insights and data-driven decision making
</p>
{% endblock %} {% block extra_css %}
<!-- Responsive Framework is already included in base template -->
<link
  rel="stylesheet"
  href="{{ url_for('static', filename='css/modern_classteacher.css') }}"
/>
<style>
  /* Premium Analytics Styling */
  body {
    background: linear-gradient(135deg, #f5f1e8 0%, #7dd3c0 50%, #4a9b8e 100%);
    min-height: 100vh;
  }

  .analytics-page-header {
    background: rgba(245, 241, 232, 0.9);
    backdrop-filter: blur(20px);
    border: 2px solid #7dd3c0;
    color: #1a2332;
    padding: var(--space-8) var(--space-6);
    border-radius: 20px;
    margin-bottom: var(--space-6);
    text-align: center;
    box-shadow: 0 8px 32px 0 rgba(44, 95, 90, 0.2);
  }

  .analytics-page-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: var(--space-2);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .analytics-page-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
  }

  .school-overview {
    background: linear-gradient(
      135deg,
      rgba(16, 185, 129, 0.1),
      rgba(34, 197, 94, 0.1)
    );
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    margin-bottom: var(--space-6);
  }

  .school-overview h3 {
    color: var(--green-600);
    margin-bottom: var(--space-4);
    display: flex;
    align-items: center;
    gap: var(--space-2);
  }

  .overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
  }

  .overview-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: var(--space-6);
    text-align: center;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .overview-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #7dd3c0, #4a9b8e);
    border-radius: 20px 20px 0 0;
  }

  .overview-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
    background: rgba(255, 255, 255, 0.25);
  }

  .overview-value {
    font-size: 3rem;
    font-weight: 800;
    background: linear-gradient(135deg, #4a9b8e, #2c5f5a);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--space-2);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .overview-label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--space-2);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .overview-trend {
    font-size: 0.8rem;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    display: inline-block;
  }

  .trend-up {
    background: var(--green-100);
    color: var(--green-800);
  }

  .trend-down {
    background: var(--red-100);
    color: var(--red-800);
  }

  .trend-stable {
    background: var(--gray-100);
    color: var(--gray-800);
  }

  .analytics-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--space-1);
    margin-bottom: var(--space-6);
    overflow-x: auto;
  }

  .analytics-tab {
    flex: 1;
    padding: var(--space-3) var(--space-4);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
  }

  .analytics-tab.active {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .analytics-tab:hover:not(.active) {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
  }

  .tab-content {
    display: none;
  }

  .tab-content.active {
    display: block;
  }

  /* Analytics Filters Section */
  .analytics-filters-section {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    margin-bottom: var(--space-6);
    backdrop-filter: blur(20px);
  }

  .filters-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-4);
    align-items: flex-end;
  }

  .filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
    min-width: 140px;
  }

  .filter-group label {
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-weight: 500;
  }

  .filter-group select {
    padding: var(--space-2);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-sm);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: all 0.3s ease;
  }

  .filter-group select:focus {
    outline: none;
    border-color: var(--primary-500);
    background: rgba(255, 255, 255, 0.08);
  }

  .filter-actions {
    display: flex;
    gap: var(--space-2);
    margin-left: auto;
  }

  .filter-status {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-sm);
    padding: var(--space-2) var(--space-3);
    margin-bottom: var(--space-3);
  }

  .filter-status span {
    color: var(--text-secondary);
    font-size: 0.85rem;
  }

  .analytics-content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-6);
    margin-bottom: var(--space-6);
    align-items: start;
  }

  .full-width-section {
    grid-column: 1 / -1;
  }

  .component-actions {
    display: flex;
    gap: var(--space-2);
    align-items: center;
    flex-wrap: wrap;
  }

  .component-actions .modern-btn {
    white-space: nowrap;
    font-size: 0.85rem;
  }

  .component-actions .btn-success {
    background: var(--success-500);
    border-color: var(--success-500);
    color: white;
  }

  .component-actions .btn-success:hover {
    background: var(--success-600);
    border-color: var(--success-600);
  }

  /* Delete Button Styles */
  .delete-btn,
  .btn-danger {
    background: #ef4444 !important;
    border-color: #ef4444 !important;
    color: white !important;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .delete-btn:hover,
  .btn-danger:hover {
    background: #dc2626 !important;
    border-color: #dc2626 !important;
    color: white !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
  }

  .delete-btn:focus,
  .btn-danger:focus {
    background: #dc2626 !important;
    border-color: #dc2626 !important;
    color: white !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
  }

  /* Individual performer delete buttons */
  .performer-actions {
    display: flex;
    gap: var(--space-1);
    align-items: center;
    margin-top: var(--space-2);
  }

  .performer-actions .delete-btn {
    font-size: 0.75rem;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
  }

  /* Make analytics sections stack vertically for better scrolling */
  .analytics-section {
    margin-bottom: var(--space-6);
  }

  /* Ensure proper spacing and visibility */
  .analytics-component.analytics-full-width {
    grid-column: 1 / -1;
  }

  /* Enhanced scrolling for long content */
  .top-performers-component,
  .subject-performance-component {
    max-height: none;
    overflow: visible;
  }

  /* Better layout for performance cards */
  .performers-grid {
    margin-bottom: var(--space-4);
  }

  .grade-section {
    margin-bottom: var(--space-6);
  }

  .stream-section {
    margin-bottom: var(--space-4);
  }

  .analytics-full-width {
    grid-column: 1 / -1;
  }

  .grade-level-analytics {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    margin-bottom: var(--space-6);
  }

  .grade-level-analytics h3 {
    color: var(--primary-color);
    margin-bottom: var(--space-4);
    display: flex;
    align-items: center;
    gap: var(--space-2);
  }

  .grade-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-4);
  }

  .grade-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    padding: var(--space-4);
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .grade-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  .grade-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-3);
  }

  .grade-name {
    font-weight: 600;
    color: var(--text-primary);
  }

  .grade-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-2);
    font-size: 0.9rem;
  }

  .stat-item {
    display: flex;
    justify-content: space-between;
  }

  .stat-label {
    color: var(--text-secondary);
  }

  .stat-value {
    font-weight: 600;
    color: var(--text-primary);
  }

  .breadcrumb {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    padding: var(--space-3) var(--space-4);
    margin-bottom: var(--space-6);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: 0.9rem;
  }

  .breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
  }

  .breadcrumb a:hover {
    text-decoration: underline;
  }

  .breadcrumb-separator {
    color: var(--text-secondary);
  }

  .analytics-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
  }

  .analytics-actions h2 {
    color: var(--primary-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-2);
  }

  .action-buttons {
    display: flex;
    gap: var(--space-2);
  }

  /* Premium Analytics Components */
  .analytics-component {
    background: rgba(245, 241, 232, 0.9);
    backdrop-filter: blur(20px);
    border: 2px solid #7dd3c0;
    border-radius: 20px;
    padding: var(--space-6);
    box-shadow: 0 8px 32px 0 rgba(44, 95, 90, 0.2);
    transition: all 0.3s ease;
    overflow: visible;
    min-height: auto;
    max-height: none;
    height: auto;
  }

  .analytics-component:hover {
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
  }

  .component-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-3);
    border-bottom: 2px solid #7dd3c0;
  }

  .component-header h3 {
    color: #1a2332;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--space-2);
  }

  .component-content {
    overflow: visible;
    max-height: none;
    height: auto;
    padding-right: var(--space-2);
  }

  .component-content::-webkit-scrollbar {
    width: 6px;
  }

  .component-content::-webkit-scrollbar-track {
    background: rgba(125, 211, 192, 0.2);
    border-radius: 3px;
  }

  .component-content::-webkit-scrollbar-thumb {
    background: #4a9b8e;
    border-radius: 3px;
  }

  .component-content::-webkit-scrollbar-thumb:hover {
    background: #2c5f5a;
  }

  .loading-state {
    text-align: center;
    padding: var(--space-8);
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
  }

  .loading-state i {
    font-size: 2rem;
    margin-bottom: var(--space-3);
    color: #667eea;
  }

  /* Performance Badge Styling */
  .performance-badge {
    padding: var(--space-1) var(--space-3);
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .performance-excellent {
    background: linear-gradient(135deg, #7dd3c0, #4a9b8e);
    color: #f5f1e8;
  }

  .performance-very-good {
    background: linear-gradient(135deg, #4a9b8e, #2c5f5a);
    color: #f5f1e8;
  }

  .performance-good {
    background: linear-gradient(135deg, #2c5f5a, #1a2332);
    color: #f5f1e8;
  }

  .performance-satisfactory {
    background: linear-gradient(135deg, #f4a261, #e09f3e);
    color: #1a2332;
  }

  .performance-needs-improvement {
    background: linear-gradient(135deg, #e76f51, #d63447);
    color: #f5f1e8;
  }

  .performance-critical {
    background: linear-gradient(135deg, #d63447, #c73650);
    color: #f5f1e8;
  }

  /* Premium Card Styling */
  .premium-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: var(--space-4);
    margin-bottom: var(--space-3);
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .premium-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.15);
  }

  .top-performer-card {
    display: flex;
    align-items: center;
    gap: var(--space-4);
  }

  .performer-rank {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-1);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-weight: bold;
    font-size: 1.1rem;
    color: white;
    flex-shrink: 0;
  }

  .rank-1 {
    background: linear-gradient(135deg, #ffd700, #ffb300);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
  }

  .rank-2 {
    background: linear-gradient(135deg, #c0c0c0, #a8a8a8);
    box-shadow: 0 4px 15px rgba(192, 192, 192, 0.4);
  }

  .rank-3 {
    background: linear-gradient(135deg, #cd7f32, #b8860b);
    box-shadow: 0 4px 15px rgba(205, 127, 50, 0.4);
  }

  .rank-other {
    background: linear-gradient(135deg, #4a9b8e, #2c5f5a);
    box-shadow: 0 4px 15px rgba(74, 155, 142, 0.4);
  }

  .performer-info {
    flex: 1;
  }

  .performer-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    margin-bottom: var(--space-1);
  }

  .performer-details {
    display: flex;
    gap: var(--space-3);
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
  }

  .performer-metrics {
    text-align: right;
  }

  .performance-score {
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
    margin-bottom: var(--space-1);
  }

  .grade-letter {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
  }

  /* Responsive Design */
  @media (max-width: 1024px) {
    .analytics-content-grid {
      grid-template-columns: 1fr;
    }

    .overview-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .filters-container {
      flex-direction: column;
      gap: var(--space-3);
    }

    .filter-group {
      min-width: auto;
    }

    .filter-actions {
      margin-left: 0;
      justify-content: center;
    }

    .component-actions {
      flex-wrap: wrap;
      gap: var(--space-1);
    }
  }

  @media (max-width: 768px) {
    .analytics-page-header {
      padding: var(--space-6) var(--space-4);
    }

    .analytics-page-header h1 {
      font-size: 2rem;
    }

    .analytics-tabs {
      flex-direction: column;
    }

    .analytics-tab {
      justify-content: flex-start;
    }

    .overview-grid {
      grid-template-columns: 1fr;
    }

    .grade-cards {
      grid-template-columns: 1fr;
    }

    .analytics-actions {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--space-3);
    }

    .action-buttons {
      width: 100%;
      justify-content: flex-start;
    }

    .component-header {
      flex-direction: column;
      gap: var(--space-3);
      align-items: flex-start;
    }

    .component-actions {
      width: 100%;
      justify-content: space-between;
    }

    .modern-btn {
      font-size: 0.8rem;
      padding: var(--space-1) var(--space-2);
    }

    .filter-actions {
      flex-direction: column;
      width: 100%;
    }

    .subjects-grid {
      grid-template-columns: 1fr;
    }
  }

  /* Scroll to Top Button */
  .scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 4px 12px rgba(44, 95, 90, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;
  }

  .scroll-to-top:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(44, 95, 90, 0.4);
  }

  .scroll-to-top.show {
    display: flex;
  }

  /* View Toggle Buttons */
  .view-toggle-group {
    display: flex;
    gap: 0;
    margin-right: var(--space-3);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(44, 95, 90, 0.15);
  }

  .view-toggle-btn {
    border-radius: 0 !important;
    border: 2px solid #7dd3c0 !important;
    margin: 0 !important;
    transition: all 0.3s ease;
  }

  .view-toggle-btn:first-child {
    border-top-left-radius: var(--radius-md) !important;
    border-bottom-left-radius: var(--radius-md) !important;
  }

  .view-toggle-btn:last-child {
    border-top-right-radius: var(--radius-md) !important;
    border-bottom-right-radius: var(--radius-md) !important;
    border-left: none !important;
  }

  .view-toggle-btn.active {
    background: #4a9b8e !important;
    color: #f5f1e8 !important;
    border-color: #4a9b8e !important;
  }

  .view-toggle-btn:not(.active) {
    background: rgba(245, 241, 232, 0.9) !important;
    color: #2c5f5a !important;
  }

  .view-toggle-btn:not(.active):hover {
    background: #7dd3c0 !important;
    color: #f5f1e8 !important;
  }
</style>
<!-- Enhanced Analytics Styles -->
<link
  rel="stylesheet"
  href="{{ url_for('static', filename='css/enhanced_analytics.css') }}"
/>
{% endblock %} {% block content %}
<div class="container">
  <!-- Breadcrumb Navigation -->
  <div class="breadcrumb">
    <a
      href="{{ url_for('admin.dashboard') }}"
      onclick="return confirmNavigation(event)"
    >
      <i class="fas fa-home"></i> Dashboard
    </a>
    <span class="breadcrumb-separator">/</span>
    <span>Academic Performance Analytics</span>
  </div>

  <!-- School Overview -->
  <div class="school-overview">
    <h3><i class="fas fa-school"></i> School Performance Overview</h3>
    <div class="overview-grid">
      <div class="overview-card">
        <div class="overview-value" id="total-students-school">
          {{ analytics_data.summary.total_students or 0 }}
        </div>
        <div class="overview-label">Total Students</div>
        <div class="overview-trend trend-up" id="students-trend">
          <i class="fas fa-arrow-up"></i> +5.2%
        </div>
      </div>
      <div class="overview-card">
        <div class="overview-value" id="total-subjects-school">
          {{ analytics_data.summary.active_subjects or 0 }}
        </div>
        <div class="overview-label">Active Subjects</div>
        <div class="overview-trend trend-stable" id="subjects-trend">
          <i class="fas fa-minus"></i> Stable
        </div>
      </div>
      <div class="overview-card">
        <div class="overview-value" id="school-average-performance">
          {{ analytics_data.summary.school_average or 0 }}%
        </div>
        <div class="overview-label">School Average</div>
        <div class="overview-trend trend-up" id="performance-trend">
          <i class="fas fa-arrow-up"></i> +2.1%
        </div>
      </div>
      <div class="overview-card">
        <div class="overview-value" id="total-teachers-active">
          {{ analytics_data.summary.active_teachers or 0 }}
        </div>
        <div class="overview-label">Active Teachers</div>
        <div class="overview-trend trend-stable" id="teachers-trend">
          <i class="fas fa-minus"></i> Stable
        </div>
      </div>
    </div>
  </div>

  <!-- Analytics Tabs -->
  <div class="analytics-tabs">
    <button
      class="analytics-tab active"
      onclick="switchTab('overview')"
      data-tab="overview"
    >
      <i class="fas fa-chart-pie"></i> Overview
    </button>
    <button
      class="analytics-tab"
      onclick="switchTab('students')"
      data-tab="students"
    >
      <i class="fas fa-users"></i> Student Performance
    </button>
    <button
      class="analytics-tab"
      onclick="switchTab('subjects')"
      data-tab="subjects"
    >
      <i class="fas fa-book"></i> Subject Analysis
    </button>
    <button
      class="analytics-tab"
      onclick="switchTab('grades')"
      data-tab="grades"
    >
      <i class="fas fa-layer-group"></i> Grade Levels
    </button>
    <button
      class="analytics-tab"
      onclick="switchTab('trends')"
      data-tab="trends"
    >
      <i class="fas fa-chart-line"></i> Trends
    </button>
  </div>

  <!-- Tab Content: Overview -->
  <div id="overview-tab" class="tab-content active">
    <!-- Analytics Filter Controls -->
    <div class="analytics-filters-section">
      <div class="filters-container">
        <div class="filter-group">
          <label for="analytics-grade-filter">Grade:</label>
          <select
            id="analytics-grade-filter"
            onchange="applyAnalyticsFilters()"
          >
            <option value="">All Grades</option>
          </select>
        </div>

        <div class="filter-group">
          <label for="analytics-stream-filter">Stream:</label>
          <select
            id="analytics-stream-filter"
            onchange="applyAnalyticsFilters()"
          >
            <option value="">All Streams</option>
          </select>
        </div>

        <div class="filter-group">
          <label for="analytics-term-filter">Term:</label>
          <select id="analytics-term-filter" onchange="applyAnalyticsFilters()">
            <option value="">All Terms</option>
          </select>
        </div>

        <div class="filter-group">
          <label for="analytics-assessment-filter">Assessment:</label>
          <select
            id="analytics-assessment-filter"
            onchange="applyAnalyticsFilters()"
          >
            <option value="">All Assessments</option>
          </select>
        </div>

        <div class="filter-actions">
          <button
            class="modern-btn btn-sm btn-outline"
            onclick="resetAnalyticsFilters()"
          >
            <i class="fas fa-undo"></i> Reset
          </button>
          <button
            class="modern-btn btn-sm btn-primary"
            onclick="exportAllAnalytics()"
          >
            <i class="fas fa-download"></i> Export All
          </button>
          <button
            class="modern-btn btn-sm btn-info"
            onclick="checkDatabaseStatus()"
            style="margin-left: var(--space-2)"
          >
            <i class="fas fa-database"></i> Check DB
          </button>
          <button
            class="modern-btn btn-sm btn-warning"
            onclick="testSimpleData()"
            style="margin-left: var(--space-2)"
          >
            <i class="fas fa-vial"></i> Test Data
          </button>
        </div>
      </div>
    </div>

    <!-- Main Analytics Content Grid -->
    <div class="analytics-content-grid">
      <!-- School Top Performers Component -->
      <div class="analytics-component top-performers-component">
        <div class="component-header">
          <h3><i class="fas fa-trophy"></i> School Top Performers</h3>
          <div class="component-actions">
            <div class="view-toggle-group">
              <button
                id="summary-view-btn"
                class="modern-btn btn-sm btn-primary view-toggle-btn active"
                onclick="togglePerformersView('summary')"
              >
                <i class="fas fa-list"></i> Summary
              </button>
              <button
                id="detailed-view-btn"
                class="modern-btn btn-sm btn-outline view-toggle-btn"
                onclick="togglePerformersView('detailed')"
              >
                <i class="fas fa-table"></i> Detailed
              </button>
            </div>
            <button
              class="modern-btn btn-sm btn-outline"
              onclick="refreshAnalytics('top_performers')"
            >
              <i class="fas fa-sync-alt"></i> Refresh
            </button>
            <button
              class="modern-btn btn-sm btn-success"
              onclick="exportSectionData('top-performers')"
            >
              <i class="fas fa-download"></i> Export
            </button>
            <button
              class="modern-btn btn-sm btn-danger"
              onclick="openBulkDeleteModal('top-performers')"
            >
              <i class="fas fa-trash-alt"></i> Delete
            </button>
          </div>
        </div>

        <div class="component-content">
          <div id="top-performers-container" class="view-mode-summary">
            <!-- Enhanced Top Performers will be loaded here -->
          </div>
        </div>
      </div>

      <!-- Subject Performance Component -->
      <div class="analytics-component subject-performance-component">
        <div class="component-header">
          <h3><i class="fas fa-chart-bar"></i> Subject Performance</h3>
          <div class="component-actions">
            <button
              class="modern-btn btn-sm btn-outline"
              onclick="refreshAnalytics('subject_performance')"
            >
              <i class="fas fa-sync-alt"></i> Refresh
            </button>
            <button
              class="modern-btn btn-sm btn-success"
              onclick="exportSectionData('subject-performance')"
            >
              <i class="fas fa-download"></i> Export
            </button>
            <button
              class="modern-btn btn-sm btn-danger"
              onclick="openBulkDeleteModal('subject-performance')"
            >
              <i class="fas fa-trash-alt"></i> Delete
            </button>
          </div>
        </div>

        <div class="component-content">
          <div id="subject-performance-container">
            <!-- Subject Performance will be loaded here -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Top Subject Performers Section -->
  <div class="analytics-section full-width-section">
    <div class="analytics-component top-subject-performers-component">
      <div class="component-header">
        <h3>
          <i class="fas fa-medal"></i> Top 3 Subject Performers (Per Grade &
          Stream)
        </h3>
        <div class="component-actions">
          <button
            class="modern-btn btn-sm btn-outline"
            onclick="refreshTopSubjectPerformers()"
          >
            <i class="fas fa-sync-alt"></i> Refresh
          </button>
          <button
            class="modern-btn btn-sm btn-success"
            onclick="exportSectionData('top-subject-performers')"
          >
            <i class="fas fa-download"></i> Export
          </button>
          <button
            class="modern-btn btn-sm btn-danger"
            onclick="openBulkDeleteModal('top-subject-performers')"
          >
            <i class="fas fa-trash-alt"></i> Delete
          </button>
        </div>
      </div>

      <!-- Enhanced Filter Controls for Top Subject Performers -->
      <div class="enhanced-filter-controls">
        <div class="filter-row">
          <div class="filter-group">
            <label for="subject-filter">Subject:</label>
            <select id="subject-filter" class="filter-select">
              <option value="">All Subjects</option>
              <!-- Will be populated dynamically -->
            </select>
          </div>
          <div class="filter-group">
            <label for="educational-level-filter">Educational Level:</label>
            <select id="educational-level-filter" class="filter-select">
              <option value="">All Levels</option>
              <option value="Grade 1">Grade 1</option>
              <option value="Grade 2">Grade 2</option>
              <option value="Grade 3">Grade 3</option>
              <option value="Grade 4">Grade 4</option>
              <option value="Grade 5">Grade 5</option>
              <option value="Grade 6">Grade 6</option>
              <option value="Grade 7">Grade 7</option>
              <option value="Grade 8">Grade 8</option>
              <option value="Grade 9">Grade 9</option>
            </select>
          </div>
          <div class="filter-group">
            <label for="exam-type-filter">Exam Type:</label>
            <select id="exam-type-filter" class="filter-select">
              <option value="">All Exam Types</option>
              <!-- Will be populated dynamically -->
            </select>
          </div>
          <div class="filter-group">
            <button
              class="modern-btn btn-sm btn-primary"
              onclick="applySubjectPerformersFilters()"
            >
              <i class="fas fa-filter"></i> Apply Filters
            </button>
            <button
              class="modern-btn btn-sm btn-outline"
              onclick="clearSubjectPerformersFilters()"
            >
              <i class="fas fa-times"></i> Clear
            </button>
          </div>
        </div>
      </div>

      <div class="component-content">
        <div class="top-performers-filter-info">
          <div class="filter-status">
            <span id="current-filters-display"
              >Showing: All Grades, All Streams, All Terms, All
              Assessments</span
            >
          </div>
        </div>

        <div id="top-subject-performers-container">
          <!-- Top Subject Performers will be loaded here -->
        </div>
      </div>
    </div>
  </div>

  <!-- Class/Stream Performance Section -->
  <div class="analytics-section">
    <div class="analytics-component">
      <div class="component-header">
        <h3><i class="fas fa-users"></i> Class & Stream Performance</h3>
        <button class="refresh-btn" onclick="loadClassStreamPerformance()">
          <i class="fas fa-sync-alt"></i> REFRESH
        </button>
      </div>

      <div class="component-content">
        <div id="class-stream-performance-container">
          <!-- Class/Stream Performance will be loaded here -->
        </div>
      </div>
    </div>
  </div>

  <!-- Tab Content: Students -->
  <div id="students-tab" class="tab-content">
    <div class="analytics-component analytics-full-width">
      <div class="component-header">
        <h3><i class="fas fa-users"></i> Student Performance Analysis</h3>
      </div>
      <div class="component-content">
        <div id="student-analytics-container">
          <div class="loading-state">
            <i class="fas fa-spinner fa-spin"></i> Loading student analytics...
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Tab Content: Subjects -->
  <div id="subjects-tab" class="tab-content">
    <div class="analytics-component analytics-full-width">
      <div class="component-header">
        <h3><i class="fas fa-book"></i> Comprehensive Subject Analysis</h3>
      </div>
      <div class="component-content">
        <div id="subject-analytics-container">
          <div class="loading-state">
            <i class="fas fa-spinner fa-spin"></i> Loading subject analytics...
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Tab Content: Grades -->
  <div id="grades-tab" class="tab-content">
    <div class="grade-level-analytics">
      <h3><i class="fas fa-layer-group"></i> Grade Level Performance</h3>
      <div class="grade-cards" id="grade-level-cards">
        <div class="loading-state">
          <i class="fas fa-spinner fa-spin"></i> Loading grade analytics...
        </div>
      </div>
    </div>
  </div>

  <!-- Tab Content: Trends -->
  <div id="trends-tab" class="tab-content">
    <div class="analytics-component analytics-full-width">
      <div class="component-header">
        <h3><i class="fas fa-chart-line"></i> Performance Trends</h3>
      </div>
      <div class="component-content">
        <div id="trends-analytics-container">
          <div class="loading-state">
            <i class="fas fa-spinner fa-spin"></i> Loading trend analytics...
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- No Data State -->
  <div id="no-data-state" class="no-data-state" style="display: none">
    <div class="no-data-icon">
      <i class="fas fa-chart-line"></i>
    </div>
    <h3>No Analytics Data Available</h3>
    <p>There isn't enough data to generate meaningful school-wide analytics.</p>
    <div class="no-data-suggestions">
      <h4>Suggestions:</h4>
      <ul>
        <li>Ensure marks have been uploaded across all classes</li>
        <li>Check that students are properly assigned to classes</li>
        <li>Verify that subjects and assessments are configured</li>
        <li>Try selecting a different term or assessment type</li>
      </ul>
    </div>
    <div style="margin-top: var(--space-4)">
      <button class="modern-btn btn-primary" onclick="resetAnalyticsFilters()">
        <i class="fas fa-undo"></i> Reset Filters
      </button>
      <a
        href="{{ url_for('admin.dashboard') }}"
        class="modern-btn btn-outline"
        style="margin-left: var(--space-2)"
      >
        <i class="fas fa-arrow-left"></i> Back to Dashboard
      </a>
    </div>
  </div>
</div>

<!-- Bulk Delete Modal -->
<div
  id="bulk-delete-modal"
  class="modal-overlay"
  style="display: none"
  onclick="closeBulkDeleteModal()"
>
  <div class="bulk-delete-modal" onclick="event.stopPropagation()">
    <div class="modal-header">
      <h3>
        <i class="fas fa-trash-alt"></i>
        <span id="modal-title">Delete Reports</span>
      </h3>
      <button class="close-modal" onclick="closeBulkDeleteModal()">
        &times;
      </button>
    </div>

    <div class="modal-body">
      <div class="delete-options">
        <div class="option-group">
          <label class="option-label">
            <input type="radio" name="delete-scope" value="all" checked />
            <span class="option-text">Delete All Reports</span>
            <small>Remove all reports in this section</small>
          </label>
        </div>

        <div class="option-group">
          <label class="option-label">
            <input type="radio" name="delete-scope" value="filtered" />
            <span class="option-text">Delete Filtered Reports</span>
            <small>Remove reports matching specific criteria</small>
          </label>
        </div>
      </div>

      <div id="filter-options" class="filter-options" style="display: none">
        <div class="filter-grid">
          <div class="filter-item">
            <label>Grade:</label>
            <select id="delete-grade-filter">
              <option value="">All Grades</option>
            </select>
          </div>

          <div class="filter-item">
            <label>Stream:</label>
            <select id="delete-stream-filter">
              <option value="">All Streams</option>
            </select>
          </div>

          <div class="filter-item">
            <label>Term:</label>
            <select id="delete-term-filter">
              <option value="">All Terms</option>
            </select>
          </div>

          <div class="filter-item">
            <label>Assessment:</label>
            <select id="delete-assessment-filter">
              <option value="">All Assessments</option>
            </select>
          </div>
        </div>
      </div>

      <div class="preview-section">
        <h4>Preview of Reports to Delete:</h4>
        <div id="delete-preview" class="delete-preview">
          <div class="loading-preview">
            <i class="fas fa-spinner fa-spin"></i> Loading preview...
          </div>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button class="modern-btn btn-outline" onclick="closeBulkDeleteModal()">
        Cancel
      </button>
      <button
        class="modern-btn btn-danger"
        onclick="confirmBulkDelete()"
        id="confirm-delete-btn"
      >
        <i class="fas fa-trash"></i> Delete Selected Reports
      </button>
    </div>
  </div>
</div>

<!-- Scroll to Top Button -->
<button class="scroll-to-top" id="scrollToTop" onclick="scrollToTop()">
  <i class="fas fa-chevron-up"></i>
</button>

<!-- Include Analytics Components Styles -->
{% include 'analytics_dashboard_components.html' %}

<!-- Enhanced Analytics Styles -->
<link
  rel="stylesheet"
  href="{{ url_for('static', filename='css/enhanced_analytics.css') }}"
/>

{% endblock %} {% block extra_js %}
<!-- Analytics Dashboard JavaScript -->
<script src="{{ url_for('static', filename='js/analytics_dashboard.js') }}?v=20250609"></script>

<script>
  // Tab switching functionality
  function switchTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll(".tab-content");
    tabContents.forEach((content) => content.classList.remove("active"));

    // Remove active class from all tabs
    const tabs = document.querySelectorAll(".analytics-tab");
    tabs.forEach((tab) => tab.classList.remove("active"));

    // Show selected tab content
    document.getElementById(tabName + "-tab").classList.add("active");

    // Add active class to selected tab
    document.querySelector(`[data-tab="${tabName}"]`).classList.add("active");

    // Load tab-specific data
    loadTabData(tabName);
  }

  function loadTabData(tabName) {
    switch (tabName) {
      case "overview":
        loadAnalyticsData();
        break;
      case "students":
        loadStudentAnalytics();
        break;
      case "subjects":
        loadSubjectAnalytics();
        break;
      case "grades":
        loadGradeAnalytics();
        break;
      case "trends":
        loadTrendAnalytics();
        break;
    }
  }

  function loadStudentAnalytics() {
    // TODO: Implement detailed student analytics
    document.getElementById("student-analytics-container").innerHTML =
      "<p>Detailed student analytics coming soon!</p>";
  }

  function loadSubjectAnalytics() {
    // TODO: Implement detailed subject analytics
    document.getElementById("subject-analytics-container").innerHTML =
      "<p>Comprehensive subject analytics coming soon!</p>";
  }

  function loadGradeAnalytics() {
    // TODO: Implement grade-level analytics
    document.getElementById("grade-level-cards").innerHTML =
      "<p>Grade-level performance analytics coming soon!</p>";
  }

  function loadTrendAnalytics() {
    // TODO: Implement trend analytics
    document.getElementById("trends-analytics-container").innerHTML =
      "<p>Performance trend analytics coming soon!</p>";
  }

  function exportSchoolAnalytics() {
    // Show export options modal
    showExportModal();
  }

  function showExportModal() {
    const modal = document.createElement("div");
    modal.className = "export-modal-overlay";
    modal.innerHTML = `
      <div class="export-modal">
        <div class="export-modal-header">
          <h3><i class="fas fa-download"></i> Export Analytics Report</h3>
          <button class="close-modal" onclick="closeExportModal()">&times;</button>
        </div>
        <div class="export-modal-body">
          <p>Choose your preferred export format:</p>
          <div class="export-options">
            <button class="export-btn pdf-btn" onclick="exportAnalytics('pdf')">
              <i class="fas fa-file-pdf"></i>
              <span>PDF Report</span>
              <small>Formatted document with charts</small>
            </button>
            <button class="export-btn word-btn" onclick="exportAnalytics('word')">
              <i class="fas fa-file-word"></i>
              <span>Word Document</span>
              <small>Editable document format</small>
            </button>
            <button class="export-btn excel-btn" onclick="exportAnalytics('excel')">
              <i class="fas fa-file-excel"></i>
              <span>Excel Spreadsheet</span>
              <small>Data analysis and charts</small>
            </button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // Add click outside to close
    modal.addEventListener("click", function (e) {
      if (e.target === modal) {
        closeExportModal();
      }
    });
  }

  function closeExportModal() {
    const modal = document.querySelector(".export-modal-overlay");
    if (modal) {
      modal.remove();
    }
  }

  async function exportAnalytics(format) {
    try {
      // Show loading state
      const exportBtn = document.querySelector(`.${format}-btn`);
      const originalContent = exportBtn.innerHTML;
      exportBtn.innerHTML =
        '<i class="fas fa-spinner fa-spin"></i> <span>Generating...</span>';
      exportBtn.disabled = true;

      // Build query parameters from current filters
      const params = new URLSearchParams();
      if (analyticsFilters.termId)
        params.append("term_id", analyticsFilters.termId);
      if (analyticsFilters.assessmentTypeId)
        params.append("assessment_type_id", analyticsFilters.assessmentTypeId);
      if (analyticsFilters.gradeId)
        params.append("grade_id", analyticsFilters.gradeId);
      if (analyticsFilters.streamId)
        params.append("stream_id", analyticsFilters.streamId);

      // Make export request
      const response = await fetch(
        `/api/analytics/export/${format}?${params.toString()}`,
        {
          method: "GET",
          credentials: "same-origin",
        }
      );

      if (!response.ok) {
        throw new Error(`Export failed: ${response.statusText}`);
      }

      // Get filename from response headers
      const contentDisposition = response.headers.get("Content-Disposition");
      let filename = `analytics_report_${new Date()
        .toISOString()
        .slice(0, 10)}.${
        format === "word" ? "docx" : format === "excel" ? "xlsx" : "pdf"
      }`;

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      // Download the file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      // Close modal and show success message
      closeExportModal();
      showNotification(
        `${format.toUpperCase()} report downloaded successfully!`,
        "success"
      );
    } catch (error) {
      console.error("Export error:", error);
      showNotification(
        `Error exporting ${format.toUpperCase()} report: ${error.message}`,
        "error"
      );

      // Restore button state
      const exportBtn = document.querySelector(`.${format}-btn`);
      if (exportBtn) {
        exportBtn.innerHTML = originalContent;
        exportBtn.disabled = false;
      }
    }
  }

  function refreshAnalyticsData() {
    loadAnalyticsData();
  }

  // Toggle between summary and detailed view for top performers
  function togglePerformersView(viewType) {
    const summaryBtn = document.getElementById("summary-view-btn");
    const detailedBtn = document.getElementById("detailed-view-btn");
    const container = document.getElementById("top-performers-container");

    // Update button states
    if (viewType === "summary") {
      summaryBtn.classList.add("active");
      summaryBtn.classList.remove("btn-outline");
      summaryBtn.classList.add("btn-primary");

      detailedBtn.classList.remove("active");
      detailedBtn.classList.add("btn-outline");
      detailedBtn.classList.remove("btn-primary");

      // Add summary view class to container
      container.classList.remove("view-mode-detailed");
      container.classList.add("view-mode-summary");
    } else {
      detailedBtn.classList.add("active");
      detailedBtn.classList.remove("btn-outline");
      detailedBtn.classList.add("btn-primary");

      summaryBtn.classList.remove("active");
      summaryBtn.classList.add("btn-outline");
      summaryBtn.classList.remove("btn-primary");

      // Add detailed view class to container
      container.classList.remove("view-mode-summary");
      container.classList.add("view-mode-detailed");
    }

    // Trigger refresh of top performers with new view type
    refreshAnalytics("top_performers", viewType);
  }

  // Enhanced refresh function with view type support
  function refreshAnalytics(component, viewType = null) {
    if (component === "top_performers") {
      // Store the current view type
      const currentViewType =
        viewType ||
        (document
          .getElementById("detailed-view-btn")
          .classList.contains("active")
          ? "detailed"
          : "summary");

      // Call the main analytics refresh with view type parameter
      if (window.loadTopPerformers) {
        window.loadTopPerformers(currentViewType);
      } else {
        // Fallback to general analytics load
        loadAnalyticsData();
      }
    } else {
      // Handle other component refreshes
      loadAnalyticsData();
    }
  }

  // Set headteacher flag for delete buttons
  window.isHeadteacher = true;

  // Helper functions to get current filter values
  function getCurrentTermName() {
    const termFilter = document.getElementById("analytics-term-filter");
    if (termFilter && termFilter.selectedOptions[0]) {
      return termFilter.selectedOptions[0].text;
    }
    return "Current Term";
  }

  function getCurrentAssessmentTypeName() {
    const assessmentFilter = document.getElementById(
      "analytics-assessment-filter"
    );
    if (assessmentFilter && assessmentFilter.selectedOptions[0]) {
      return assessmentFilter.selectedOptions[0].text;
    }
    return "Current Assessment";
  }

  // Navigation confirmation function
  function confirmNavigation(event) {
    // Check if user is authenticated as headteacher
    const role = "{{ session.role }}";
    const isAuthenticated = "{{ session.username }}";

    if (!isAuthenticated || role !== "headteacher") {
      event.preventDefault();
      showNotification(
        "Please ensure you are logged in as headteacher",
        "warning"
      );
      return false;
    }

    // Allow navigation
    return true;
  }

  // Initialize analytics when page loads
  document.addEventListener("DOMContentLoaded", function () {
    console.log("Headteacher analytics page - initializing dashboard");
    // The analytics dashboard will be initialized by the main analytics_dashboard.js file
    // which now checks for analytics page elements before initializing
    loadSchoolOverview();
  });

  function loadSchoolOverview() {
    // Load real school overview statistics from server-side data
    // These values are already populated by the server-side template
    console.log("School overview loaded from server-side data");

    // Load filter options first
    loadAnalyticsFilterOptions();

    // Enhanced analytics will be loaded by the main analytics_dashboard.js
    // No need to call them here as they're handled by the main dashboard initialization

    // Load top subject performers
    loadTopSubjectPerformers();

    // Load filter options for subject performers
    loadSubjectPerformersFilterOptions();

    // Initialize filter status display
    updateFilterStatusDisplay();
  }

  async function loadTopSubjectPerformers() {
    const container = document.getElementById(
      "top-subject-performers-container"
    );
    if (!container) return;

    try {
      // Show loading state
      container.innerHTML = `
        <div class="loading-state">
          <i class="fas fa-spinner fa-spin"></i> Loading top subject performers...
        </div>
      `;

      // Build query parameters from current filters
      const params = new URLSearchParams();
      if (analyticsFilters.termId)
        params.append("term_id", analyticsFilters.termId);
      if (analyticsFilters.assessmentTypeId)
        params.append("assessment_type_id", analyticsFilters.assessmentTypeId);
      if (analyticsFilters.gradeId)
        params.append("grade_id", analyticsFilters.gradeId);
      if (analyticsFilters.streamId)
        params.append("stream_id", analyticsFilters.streamId);

      // Add enhanced filters
      const subjectFilter = document.getElementById("subject-filter");
      const educationalLevelFilter = document.getElementById(
        "educational-level-filter"
      );
      const examTypeFilter = document.getElementById("exam-type-filter");

      if (subjectFilter && subjectFilter.value)
        params.append("subject_id", subjectFilter.value);
      if (educationalLevelFilter && educationalLevelFilter.value)
        params.append("educational_level", educationalLevelFilter.value);
      if (examTypeFilter && examTypeFilter.value)
        params.append("exam_type", examTypeFilter.value);

      const response = await fetch(
        `/api/analytics/top_subject_performers?${params.toString()}`,
        {
          credentials: "same-origin",
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        displayTopSubjectPerformers(data.top_subject_performers);
      } else {
        throw new Error(
          data.message || "Failed to load top subject performers"
        );
      }
    } catch (error) {
      console.error("Error loading top subject performers:", error);
      container.innerHTML = `
        <div class="error-state">
          <i class="fas fa-exclamation-triangle"></i>
          <p>Error loading top subject performers: ${error.message}</p>
          <div class="error-actions">
            <button onclick="loadTopSubjectPerformers()" class="retry-btn modern-btn btn-sm btn-primary">
              <i class="fas fa-redo"></i> Retry
            </button>
            <button onclick="testSimpleData()" class="modern-btn btn-sm btn-warning" style="margin-left: var(--space-2);">
              <i class="fas fa-vial"></i> Test Data
            </button>
            <button onclick="checkDatabaseStatus()" class="modern-btn btn-sm btn-info" style="margin-left: var(--space-2);">
              <i class="fas fa-database"></i> Check DB
            </button>
          </div>
          <div class="error-suggestions" style="margin-top: var(--space-3); padding: var(--space-3); background: rgba(255,255,255,0.05); border-radius: var(--radius-md);">
            <h4>Troubleshooting Steps:</h4>
            <ol style="text-align: left; margin: var(--space-2) 0;">
              <li>Click "Check DB" to verify database has data</li>
              <li>Click "Test Data" to test simple data access</li>
              <li>Ensure marks have been uploaded through Class Teacher interface</li>
              <li>Verify students are assigned to streams and grades</li>
              <li>Check that subjects and assessment types are configured</li>
            </ol>
          </div>
        </div>
      `;
    }
  }

  function displayTopSubjectPerformers(data) {
    const container = document.getElementById(
      "top-subject-performers-container"
    );
    if (!container) return;

    if (!data || Object.keys(data).length === 0) {
      container.innerHTML = `
        <div class="no-data-message">
          <i class="fas fa-medal"></i>
          <p>No top subject performers data available</p>
        </div>
      `;
      return;
    }

    let html = '<div class="top-subject-performers-grid">';

    Object.entries(data).forEach(([gradeName, streams]) => {
      html += `
        <div class="grade-section">
          <h3 class="grade-title">
            <i class="fas fa-layer-group"></i> ${gradeName}
          </h3>
          <div class="streams-container">
      `;

      Object.entries(streams).forEach(([streamName, subjects]) => {
        html += `
          <div class="stream-section">
            <h4 class="stream-title">
              <i class="fas fa-users"></i> ${streamName}
            </h4>
            <div class="subjects-grid">
        `;

        Object.entries(subjects).forEach(([subjectName, subjectData]) => {
          const topPerformers = subjectData.top_performers || [];
          const totalStudents = subjectData.total_students || 0;

          html += `
            <div class="subject-card">
              <div class="subject-header">
                <div class="subject-name">${subjectName}</div>
                <div class="subject-stats">
                  <span class="total-students">${totalStudents} students</span>
                </div>
                <div class="subject-actions">
                  <button class="delete-report-btn"
                          onclick="deleteSubjectReport('${gradeName.replace(
                            "Grade ",
                            ""
                          )}', '${streamName.replace(
            "Stream ",
            ""
          )}', '${subjectName}')"
                          title="Delete marks for this subject">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>

              <div class="top-performers-list">
          `;

          if (topPerformers.length === 0) {
            html +=
              '<div class="no-performers">No performance data available</div>';
          } else {
            topPerformers.forEach((performer, index) => {
              const medalIcon = index === 0 ? "🥇" : index === 1 ? "🥈" : "🥉";
              const gradeClass = getGradeClass(performer.grade_letter);

              html += `
                <div class="performer-item">
                  <div class="performer-rank">
                    <span class="medal">${medalIcon}</span>
                    <span class="position">#${performer.position}</span>
                  </div>
                  <div class="performer-info">
                    <div class="performer-name">${performer.student_name}</div>
                    <div class="performer-admission">${performer.admission_number}</div>
                  </div>
                  <div class="performer-score">
                    <div class="score-marks">${performer.marks}/${performer.total_marks}</div>
                    <div class="score-percentage">${performer.percentage}%</div>
                    <div class="score-grade ${gradeClass}">${performer.grade_letter}</div>
                  </div>
                </div>
              `;
            });
          }

          html += `
              </div>
            </div>
          `;
        });

        html += `
            </div>
          </div>
        `;
      });

      html += `
          </div>
        </div>
      `;
    });

    html += "</div>";
    container.innerHTML = html;
  }

  function refreshTopSubjectPerformers() {
    loadTopSubjectPerformers();
  }

  // Database status check function
  async function checkDatabaseStatus() {
    try {
      showNotification("Checking database status...", "info");

      const response = await fetch("/api/analytics/database_status", {
        credentials: "same-origin",
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        const status = data.database_status;
        const message = `Database Status:
        • Marks: ${status.marks_count}
        • Students: ${status.students_count}
        • Subjects: ${status.subjects_count}
        • Grades: ${status.grades_count}
        • Streams: ${status.streams_count}
        • Terms: ${status.terms_count}
        • Assessment Types: ${status.assessment_types_count}`;

        alert(message);
        console.log("Database Status:", status);

        if (status.marks_count === 0) {
          showNotification("No marks data found in database", "warning");
        } else {
          showNotification(
            `Database has ${status.marks_count} marks records`,
            "success"
          );
        }
      } else {
        throw new Error(data.message || "Failed to get database status");
      }
    } catch (error) {
      console.error("Error checking database status:", error);
      showNotification(`Error checking database: ${error.message}`, "error");
    }
  }

  // Test simple data function
  async function testSimpleData() {
    try {
      showNotification("Testing simple data access...", "info");

      const response = await fetch("/api/analytics/test_simple_data", {
        credentials: "same-origin",
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        const testData = data.test_data;
        console.log("Test Data:", testData);

        const message = `Test Results:
        • Marks: ${testData.marks_count}
        • Students: ${testData.students_count}
        • Sample marks processed: ${testData.sample_marks_with_details.length}`;

        alert(message);

        if (testData.sample_marks_with_details.length > 0) {
          showNotification("Simple data access working correctly", "success");
          console.log(
            "Sample marks with details:",
            testData.sample_marks_with_details
          );
        } else {
          showNotification("No sample data could be processed", "warning");
        }
      } else {
        throw new Error(data.message || "Failed to test simple data");
      }
    } catch (error) {
      console.error("Error testing simple data:", error);
      showNotification(`Error testing data: ${error.message}`, "error");
    }
  }

  // Analytics Filter Functions
  async function loadAnalyticsFilterOptions() {
    try {
      const response = await fetch("/api/analytics/filter_options", {
        credentials: "same-origin",
      });

      if (!response.ok) throw new Error("Failed to load filter options");

      const data = await response.json();

      if (data.success) {
        // Populate grade filter
        const gradeFilter = document.getElementById("analytics-grade-filter");
        gradeFilter.innerHTML = '<option value="">All Grades</option>';
        data.grades.forEach((grade) => {
          gradeFilter.innerHTML += `<option value="${grade.id}">${grade.name}</option>`;
        });

        // Populate stream filter
        const streamFilter = document.getElementById("analytics-stream-filter");
        streamFilter.innerHTML = '<option value="">All Streams</option>';
        data.streams.forEach((stream) => {
          streamFilter.innerHTML += `<option value="${stream.id}">Stream ${stream.name}</option>`;
        });

        // Populate term filter
        const termFilter = document.getElementById("analytics-term-filter");
        termFilter.innerHTML = '<option value="">All Terms</option>';
        data.terms.forEach((term) => {
          termFilter.innerHTML += `<option value="${term.id}">${term.name}</option>`;
        });

        // Populate assessment filter
        const assessmentFilter = document.getElementById(
          "analytics-assessment-filter"
        );
        assessmentFilter.innerHTML =
          '<option value="">All Assessments</option>';
        data.assessment_types.forEach((assessment) => {
          assessmentFilter.innerHTML += `<option value="${assessment.id}">${assessment.name}</option>`;
        });
      }
    } catch (error) {
      console.error("Error loading filter options:", error);
      showNotification("Error loading filter options", "error");
    }
  }

  function applyAnalyticsFilters() {
    // Update global filters
    analyticsFilters.gradeId = document.getElementById(
      "analytics-grade-filter"
    ).value;
    analyticsFilters.streamId = document.getElementById(
      "analytics-stream-filter"
    ).value;
    analyticsFilters.termId = document.getElementById(
      "analytics-term-filter"
    ).value;
    analyticsFilters.assessmentTypeId = document.getElementById(
      "analytics-assessment-filter"
    ).value;

    // Update filter status display
    updateFilterStatusDisplay();

    // Reload all analytics data
    loadAnalyticsData();
    loadTopSubjectPerformers();
  }

  function resetAnalyticsFilters() {
    document.getElementById("analytics-grade-filter").value = "";
    document.getElementById("analytics-stream-filter").value = "";
    document.getElementById("analytics-term-filter").value = "";
    document.getElementById("analytics-assessment-filter").value = "";

    applyAnalyticsFilters();
  }

  function updateFilterStatusDisplay() {
    const gradeText =
      document.getElementById("analytics-grade-filter").selectedOptions[0]
        ?.text || "All Grades";
    const streamText =
      document.getElementById("analytics-stream-filter").selectedOptions[0]
        ?.text || "All Streams";
    const termText =
      document.getElementById("analytics-term-filter").selectedOptions[0]
        ?.text || "All Terms";
    const assessmentText =
      document.getElementById("analytics-assessment-filter").selectedOptions[0]
        ?.text || "All Assessments";

    const display = document.getElementById("current-filters-display");
    if (display) {
      display.textContent = `Showing: ${gradeText}, ${streamText}, ${termText}, ${assessmentText}`;
    }
  }

  // Enhanced Filter Functions for Top Subject Performers
  function applySubjectPerformersFilters() {
    console.log("Applying subject performers filters...");
    loadTopSubjectPerformers();
  }

  function clearSubjectPerformersFilters() {
    console.log("Clearing subject performers filters...");

    // Clear filter selections
    const subjectFilter = document.getElementById("subject-filter");
    const educationalLevelFilter = document.getElementById(
      "educational-level-filter"
    );
    const examTypeFilter = document.getElementById("exam-type-filter");

    if (subjectFilter) subjectFilter.value = "";
    if (educationalLevelFilter) educationalLevelFilter.value = "";
    if (examTypeFilter) examTypeFilter.value = "";

    // Reload data
    loadTopSubjectPerformers();
  }

  // Load filter options
  async function loadSubjectPerformersFilterOptions() {
    try {
      // Load subjects and assessment types
      const response = await fetch("/api/analytics/context_options", {
        credentials: "same-origin",
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Populate subject filter
          const subjectFilter = document.getElementById("subject-filter");
          if (subjectFilter && data.options.subjects) {
            subjectFilter.innerHTML = '<option value="">All Subjects</option>';
            data.options.subjects.forEach((subject) => {
              const option = document.createElement("option");
              option.value = subject.id;
              option.textContent = subject.name;
              subjectFilter.appendChild(option);
            });
          }

          // Populate exam type filter
          const examTypeFilter = document.getElementById("exam-type-filter");
          if (examTypeFilter && data.options.assessment_types) {
            examTypeFilter.innerHTML =
              '<option value="">All Exam Types</option>';
            data.options.assessment_types.forEach((assessmentType) => {
              const option = document.createElement("option");
              option.value = assessmentType.name;
              option.textContent = assessmentType.name;
              examTypeFilter.appendChild(option);
            });
          }
        }
      }
    } catch (error) {
      console.error("Error loading filter options:", error);
    }
  }

  // Export Functions
  async function exportSectionData(section) {
    try {
      showNotification("Preparing export...", "info");

      // Build parameters
      const params = new URLSearchParams();
      params.append("section", section);
      params.append("format", "excel"); // Default to Excel

      if (analyticsFilters.gradeId)
        params.append("grade_id", analyticsFilters.gradeId);
      if (analyticsFilters.streamId)
        params.append("stream_id", analyticsFilters.streamId);
      if (analyticsFilters.termId)
        params.append("term_id", analyticsFilters.termId);
      if (analyticsFilters.assessmentTypeId)
        params.append("assessment_type_id", analyticsFilters.assessmentTypeId);

      const response = await fetch(
        `/api/analytics/export?${params.toString()}`,
        {
          credentials: "same-origin",
        }
      );

      if (!response.ok) throw new Error("Export failed");

      // Download the file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `${section}-analytics-${
        new Date().toISOString().split("T")[0]
      }.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      showNotification("Export completed successfully!", "success");
    } catch (error) {
      console.error("Export error:", error);
      showNotification(`Export failed: ${error.message}`, "error");
    }
  }

  async function exportAllAnalytics() {
    try {
      showNotification("Preparing comprehensive export...", "info");

      // Build parameters
      const params = new URLSearchParams();
      params.append("section", "all");
      params.append("format", "excel");

      if (analyticsFilters.gradeId)
        params.append("grade_id", analyticsFilters.gradeId);
      if (analyticsFilters.streamId)
        params.append("stream_id", analyticsFilters.streamId);
      if (analyticsFilters.termId)
        params.append("term_id", analyticsFilters.termId);
      if (analyticsFilters.assessmentTypeId)
        params.append("assessment_type_id", analyticsFilters.assessmentTypeId);

      const response = await fetch(
        `/api/analytics/export?${params.toString()}`,
        {
          credentials: "same-origin",
        }
      );

      if (!response.ok) throw new Error("Export failed");

      // Download the file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `complete-analytics-${
        new Date().toISOString().split("T")[0]
      }.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      showNotification("Complete export finished!", "success");
    } catch (error) {
      console.error("Export error:", error);
      showNotification(`Export failed: ${error.message}`, "error");
    }
  }

  // Delete subject report function
  function deleteSubjectReport(grade, stream, subject) {
    // Validate parameters
    if (!grade || !stream || !subject) {
      showNotification("Invalid parameters for delete operation", "error");
      console.error("Delete parameters:", { grade, stream, subject });
      return;
    }

    // Get current filter values for term and assessment type
    const termFilter = document.getElementById("analytics-term-filter");
    const assessmentFilter = document.getElementById(
      "analytics-assessment-filter"
    );

    let termName = "Current Term";
    let assessmentTypeName = "Current Assessment";

    if (termFilter && termFilter.selectedOptions[0] && termFilter.value) {
      termName = termFilter.selectedOptions[0].text;
    }

    if (
      assessmentFilter &&
      assessmentFilter.selectedOptions[0] &&
      assessmentFilter.value
    ) {
      assessmentTypeName = assessmentFilter.selectedOptions[0].text;
    }

    // Clean up parameters
    const cleanGrade = grade.toString().trim();
    const cleanStream = stream.toString().trim();
    const cleanSubject = subject.toString().trim();
    const cleanTerm = termName.toString().trim();
    const cleanAssessmentType = assessmentTypeName.toString().trim();

    if (
      !confirm(
        `Are you sure you want to delete marks for ${cleanSubject} in Grade ${cleanGrade} Stream ${cleanStream} (${cleanTerm} - ${cleanAssessmentType})? This will delete all associated marks and cannot be undone.`
      )
    ) {
      return;
    }

    // Show loading state
    showNotification("Deleting subject marks...", "info");

    // Create form data
    const formData = new FormData();
    formData.append("grade", cleanGrade);
    formData.append("stream", cleanStream);
    formData.append("term", cleanTerm);
    formData.append("assessment_type", cleanAssessmentType);
    formData.append("subject", cleanSubject);

    // Send delete request
    fetch("/api/analytics/delete_subject_marks", {
      method: "POST",
      body: formData,
      credentials: "same-origin",
      headers: {
        "X-Requested-With": "XMLHttpRequest",
      },
    })
      .then(async (response) => {
        if (!response.ok) {
          const errorText = await response.text();
          console.error("Delete response error:", errorText);
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
      })
      .then((data) => {
        if (data.success) {
          showNotification(data.message, "success");
          // Refresh analytics data
          loadAnalyticsData();
          loadTopSubjectPerformers();
        } else {
          showNotification(
            data.message || "Failed to delete subject marks",
            "error"
          );
        }
      })
      .catch((error) => {
        console.error("Error deleting subject marks:", error);
        showNotification(
          `Error deleting subject marks: ${error.message}`,
          "error"
        );
      });
  }

  // Delete report function for analytics
  function deleteReportFromAnalytics(grade, stream, term, assessmentType) {
    // Debug logging
    console.log("🗑️ deleteReportFromAnalytics called with:", {
      grade: grade,
      stream: stream,
      term: term,
      assessmentType: assessmentType,
      types: {
        grade: typeof grade,
        stream: typeof stream,
        term: typeof term,
        assessmentType: typeof assessmentType,
      },
    });

    // Validate parameters - allow "Unknown" and "All" values
    if (
      !grade ||
      !stream ||
      !term ||
      !assessmentType ||
      grade === "undefined" ||
      stream === "undefined" ||
      term === "undefined" ||
      assessmentType === "undefined"
    ) {
      console.error("❌ Invalid parameters detected:", {
        grade: grade,
        stream: stream,
        term: term,
        assessmentType: assessmentType,
      });
      showNotification(
        "Invalid parameters for delete operation. Please refresh the page and try again.",
        "error"
      );
      return;
    }

    // Clean up parameters
    const cleanGrade = grade.toString().trim();
    const cleanStream = stream.toString().trim();
    const cleanTerm = term.toString().trim();
    const cleanAssessmentType = assessmentType.toString().trim();

    if (
      !confirm(
        `Are you sure you want to delete the report for Grade ${cleanGrade} Stream ${cleanStream} in ${cleanTerm} ${cleanAssessmentType}? This will delete all associated marks and cannot be undone.`
      )
    ) {
      return;
    }

    // Show loading state
    showNotification("Deleting report...", "info");

    // Create form data
    const formData = new FormData();
    formData.append("grade", cleanGrade);
    formData.append("stream", cleanStream);
    formData.append("term", cleanTerm);
    formData.append("assessment_type", cleanAssessmentType);

    // Send delete request
    fetch("/api/analytics/delete_report", {
      method: "POST",
      body: formData,
      credentials: "same-origin",
      headers: {
        "X-Requested-With": "XMLHttpRequest",
      },
    })
      .then(async (response) => {
        if (!response.ok) {
          const errorText = await response.text();
          console.error("Delete response error:", errorText);
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
      })
      .then((data) => {
        if (data.success) {
          showNotification(data.message, "success");
          // Refresh analytics data
          loadAnalyticsData();
          loadTopSubjectPerformers();
        } else {
          showNotification(data.message || "Failed to delete report", "error");
        }
      })
      .catch((error) => {
        console.error("Error deleting report:", error);
        showNotification(`Error deleting report: ${error.message}`, "error");
      });
  }

  // Bulk Delete Functions
  let currentDeleteSection = "";

  function openBulkDeleteModal(section) {
    currentDeleteSection = section;
    const modal = document.getElementById("bulk-delete-modal");
    const modalTitle = document.getElementById("modal-title");

    // Set modal title based on section
    if (section === "top-performers") {
      modalTitle.textContent = "Delete Top Performers Reports";
    } else if (section === "subject-performance") {
      modalTitle.textContent = "Delete Subject Performance Reports";
    } else if (section === "top-subject-performers") {
      modalTitle.textContent = "Delete Top Subject Performers Reports";
    }

    // Show modal
    modal.style.display = "flex";

    // Load filter options
    loadDeleteFilterOptions();

    // Set up event listeners
    setupDeleteModalListeners();

    // Load initial preview
    loadDeletePreview();
  }

  function closeBulkDeleteModal() {
    const modal = document.getElementById("bulk-delete-modal");
    modal.style.display = "none";
  }

  function setupDeleteModalListeners() {
    // Radio button change listeners
    const radioButtons = document.querySelectorAll(
      'input[name="delete-scope"]'
    );
    radioButtons.forEach((radio) => {
      radio.addEventListener("change", function () {
        const filterOptions = document.getElementById("filter-options");
        if (this.value === "filtered") {
          filterOptions.style.display = "block";
        } else {
          filterOptions.style.display = "none";
        }
        loadDeletePreview();
      });
    });

    // Filter change listeners
    const filters = [
      "delete-grade-filter",
      "delete-stream-filter",
      "delete-term-filter",
      "delete-assessment-filter",
    ];
    filters.forEach((filterId) => {
      const filter = document.getElementById(filterId);
      if (filter) {
        filter.addEventListener("change", loadDeletePreview);
      }
    });
  }

  async function loadDeleteFilterOptions() {
    try {
      // Load available options for filters
      const response = await fetch("/api/analytics/filter_options", {
        credentials: "same-origin",
      });

      if (!response.ok) throw new Error("Failed to load filter options");

      const data = await response.json();

      // Populate grade filter
      const gradeFilter = document.getElementById("delete-grade-filter");
      gradeFilter.innerHTML = '<option value="">All Grades</option>';
      data.grades.forEach((grade) => {
        gradeFilter.innerHTML += `<option value="${grade.id}">${grade.name}</option>`;
      });

      // Populate stream filter
      const streamFilter = document.getElementById("delete-stream-filter");
      streamFilter.innerHTML = '<option value="">All Streams</option>';
      data.streams.forEach((stream) => {
        streamFilter.innerHTML += `<option value="${stream.id}">Stream ${stream.name}</option>`;
      });

      // Populate term filter
      const termFilter = document.getElementById("delete-term-filter");
      termFilter.innerHTML = '<option value="">All Terms</option>';
      data.terms.forEach((term) => {
        termFilter.innerHTML += `<option value="${term.id}">${term.name}</option>`;
      });

      // Populate assessment filter
      const assessmentFilter = document.getElementById(
        "delete-assessment-filter"
      );
      assessmentFilter.innerHTML = '<option value="">All Assessments</option>';
      data.assessment_types.forEach((assessment) => {
        assessmentFilter.innerHTML += `<option value="${assessment.id}">${assessment.name}</option>`;
      });
    } catch (error) {
      console.error("Error loading filter options:", error);
      showNotification("Error loading filter options", "error");
    }
  }

  async function loadDeletePreview() {
    const previewContainer = document.getElementById("delete-preview");
    previewContainer.innerHTML =
      '<div class="loading-preview"><i class="fas fa-spinner fa-spin"></i> Loading preview...</div>';

    try {
      const deleteScope = document.querySelector(
        'input[name="delete-scope"]:checked'
      ).value;

      // Build parameters
      const params = new URLSearchParams();
      params.append("section", currentDeleteSection);
      params.append("scope", deleteScope);

      if (deleteScope === "filtered") {
        const gradeFilter = document.getElementById(
          "delete-grade-filter"
        ).value;
        const streamFilter = document.getElementById(
          "delete-stream-filter"
        ).value;
        const termFilter = document.getElementById("delete-term-filter").value;
        const assessmentFilter = document.getElementById(
          "delete-assessment-filter"
        ).value;

        if (gradeFilter) params.append("grade_id", gradeFilter);
        if (streamFilter) params.append("stream_id", streamFilter);
        if (termFilter) params.append("term_id", termFilter);
        if (assessmentFilter)
          params.append("assessment_type_id", assessmentFilter);
      }

      const response = await fetch(
        `/api/analytics/delete_preview?${params.toString()}`,
        {
          credentials: "same-origin",
        }
      );

      if (!response.ok) throw new Error("Failed to load preview");

      const data = await response.json();

      if (data.success) {
        displayDeletePreview(data);
      } else {
        throw new Error(data.message || "Failed to load preview");
      }
    } catch (error) {
      console.error("Error loading delete preview:", error);
      previewContainer.innerHTML = `<div class="error-preview">Error loading preview: ${error.message}</div>`;
    }
  }

  function displayDeletePreview(data) {
    const previewContainer = document.getElementById("delete-preview");
    const confirmBtn = document.getElementById("confirm-delete-btn");

    if (data.reports_count === 0) {
      previewContainer.innerHTML =
        '<div class="no-reports-preview">No reports match the selected criteria.</div>';
      confirmBtn.disabled = true;
      return;
    }

    confirmBtn.disabled = false;

    let html = `
      <div class="preview-summary">
        <div class="preview-stat">
          <span class="stat-number">${data.reports_count}</span>
          <span class="stat-label">Reports</span>
        </div>
        <div class="preview-stat">
          <span class="stat-number">${data.marks_count}</span>
          <span class="stat-label">Marks</span>
        </div>
        <div class="preview-stat">
          <span class="stat-number">${data.students_affected}</span>
          <span class="stat-label">Students Affected</span>
        </div>
      </div>
    `;

    if (data.preview_items && data.preview_items.length > 0) {
      html += '<div class="preview-items">';
      data.preview_items.forEach((item) => {
        html += `
          <div class="preview-item">
            <span class="item-name">${item.description}</span>
            <span class="item-count">${item.marks_count} marks</span>
          </div>
        `;
      });
      html += "</div>";
    }

    previewContainer.innerHTML = html;
  }

  async function confirmBulkDelete() {
    const deleteScope = document.querySelector(
      'input[name="delete-scope"]:checked'
    ).value;

    // Get confirmation
    const confirmMessage =
      deleteScope === "all"
        ? `Are you sure you want to delete ALL reports in the ${currentDeleteSection} section? This action cannot be undone.`
        : `Are you sure you want to delete the selected reports? This action cannot be undone.`;

    if (!confirm(confirmMessage)) {
      return;
    }

    // Show loading state
    const confirmBtn = document.getElementById("confirm-delete-btn");
    const originalText = confirmBtn.innerHTML;
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';
    confirmBtn.disabled = true;

    try {
      // Build parameters
      const params = new URLSearchParams();
      params.append("section", currentDeleteSection);
      params.append("scope", deleteScope);

      if (deleteScope === "filtered") {
        const gradeFilter = document.getElementById(
          "delete-grade-filter"
        ).value;
        const streamFilter = document.getElementById(
          "delete-stream-filter"
        ).value;
        const termFilter = document.getElementById("delete-term-filter").value;
        const assessmentFilter = document.getElementById(
          "delete-assessment-filter"
        ).value;

        if (gradeFilter) params.append("grade_id", gradeFilter);
        if (streamFilter) params.append("stream_id", streamFilter);
        if (termFilter) params.append("term_id", termFilter);
        if (assessmentFilter)
          params.append("assessment_type_id", assessmentFilter);
      }

      const response = await fetch("/api/analytics/bulk_delete", {
        method: "POST",
        body: params,
        credentials: "same-origin",
      });

      const data = await response.json();

      if (data.success) {
        showNotification(data.message, "success");
        closeBulkDeleteModal();
        // Refresh analytics data
        loadAnalyticsData();
      } else {
        throw new Error(data.message || "Failed to delete reports");
      }
    } catch (error) {
      console.error("Error deleting reports:", error);
      showNotification(`Error deleting reports: ${error.message}`, "error");
    } finally {
      // Restore button state
      confirmBtn.innerHTML = originalText;
      confirmBtn.disabled = false;
    }
  }

  // Utility function for notifications
  function showNotification(message, type = "info") {
    const notification = document.createElement("div");
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
            <i class="fas fa-info-circle"></i>
            <span>${message}</span>
        `;

    document.body.appendChild(notification);

    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  // Scroll to top functionality
  function scrollToTop() {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }

  // Show/hide scroll to top button based on scroll position
  function handleScroll() {
    const scrollToTopBtn = document.getElementById("scrollToTop");
    if (scrollToTopBtn) {
      if (window.pageYOffset > 300) {
        scrollToTopBtn.classList.add("show");
      } else {
        scrollToTopBtn.classList.remove("show");
      }
    }
  }

  // Initialize scroll functionality when page loads
  document.addEventListener("DOMContentLoaded", function () {
    // Add scroll event listener
    window.addEventListener("scroll", handleScroll);
  });
</script>

<style>
  .notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--primary-color);
    color: white !important;
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-md);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    animation: slideIn 0.3s ease-out;
    font-size: 0.9rem;
    font-weight: 500;
    min-width: 300px;
  }

  .notification-info {
    background: #3b82f6 !important;
    color: white !important;
  }
  .notification-success {
    background: #10b981 !important;
    color: white !important;
  }
  .notification-warning {
    background: #f59e0b !important;
    color: white !important;
  }
  .notification-error {
    background: #ef4444 !important;
    color: white !important;
  }

  .notification span {
    color: white !important;
  }

  .notification i {
    color: white !important;
  }

  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  /* Export Modal Styles */
  .export-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    animation: fadeIn 0.3s ease;
  }

  .export-modal {
    background: linear-gradient(
      135deg,
      #f5f1e8 0%,
      rgba(125, 211, 192, 0.1) 100%
    );
    border: 2px solid #7dd3c0;
    border-radius: 20px;
    padding: var(--space-6);
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(44, 95, 90, 0.3);
    backdrop-filter: blur(20px);
    animation: slideUp 0.3s ease;
  }

  .export-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-3);
    border-bottom: 2px solid #7dd3c0;
  }

  .export-modal-header h3 {
    color: var(--primary-color);
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: var(--space-2);
  }

  .close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-1);
    border-radius: 50%;
    transition: all 0.3s ease;
  }

  .close-modal:hover {
    background: rgba(44, 95, 90, 0.1);
    color: var(--primary-color);
  }

  .export-options {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    margin-top: var(--space-4);
  }

  .export-btn {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid transparent;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
  }

  .export-btn:hover {
    background: rgba(125, 211, 192, 0.2);
    border-color: #7dd3c0;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(44, 95, 90, 0.2);
  }

  .export-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .export-btn i {
    font-size: 2rem;
    width: 40px;
    text-align: center;
  }

  .pdf-btn i {
    color: #dc3545;
  }
  .word-btn i {
    color: #0d6efd;
  }
  .excel-btn i {
    color: #198754;
  }

  .export-btn span {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.1rem;
  }

  .export-btn small {
    color: var(--text-secondary);
    font-size: 0.85rem;
    margin-top: var(--space-1);
    display: block;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      transform: translateY(30px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* Delete button styles for analytics */
  .delete-report-btn {
    background: var(--red-500);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    padding: var(--space-1) var(--space-2);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: var(--space-2);
  }

  .delete-report-btn:hover {
    background: var(--red-600);
    transform: scale(1.05);
  }

  .class-actions {
    margin-top: var(--space-2);
    text-align: right;
  }

  .performer-actions {
    margin-left: auto;
    display: flex;
    align-items: center;
  }

  .detailed-actions {
    margin-top: var(--space-2);
    text-align: right;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: var(--space-2);
  }

  .subject-actions {
    margin-left: auto;
  }

  .delete-report-btn {
    opacity: 0.7;
    transition: all 0.3s ease;
  }

  .delete-report-btn:hover {
    opacity: 1;
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
  }

  /* Bulk Delete Modal Styles */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
  }

  .bulk-delete-modal {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(20px);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  }

  .modal-header {
    padding: var(--space-4);
    border-bottom: 1px solid var(--glass-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .modal-header h3 {
    margin: 0;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--space-2);
  }

  .close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    transition: all 0.3s ease;
  }

  .close-modal:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
  }

  .modal-body {
    padding: var(--space-4);
  }

  .delete-options {
    margin-bottom: var(--space-4);
  }

  .option-group {
    margin-bottom: var(--space-3);
  }

  .option-label {
    display: flex;
    align-items: flex-start;
    gap: var(--space-2);
    cursor: pointer;
    padding: var(--space-3);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
  }

  .option-label:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: var(--primary-500);
  }

  .option-label input[type="radio"] {
    margin-top: 2px;
  }

  .option-text {
    font-weight: 500;
    color: var(--text-primary);
    display: block;
    margin-bottom: var(--space-1);
  }

  .option-label small {
    color: var(--text-secondary);
    font-size: 0.85rem;
  }

  .filter-options {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    padding: var(--space-4);
    margin-bottom: var(--space-4);
  }

  .filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: var(--space-3);
  }

  .filter-item label {
    display: block;
    margin-bottom: var(--space-1);
    color: var(--text-secondary);
    font-size: 0.9rem;
  }

  .filter-item select {
    width: 100%;
    padding: var(--space-2);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-sm);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    font-size: 0.9rem;
  }

  .preview-section h4 {
    margin-bottom: var(--space-3);
    color: var(--text-primary);
  }

  .delete-preview {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    padding: var(--space-4);
    min-height: 120px;
  }

  .preview-summary {
    display: flex;
    gap: var(--space-4);
    margin-bottom: var(--space-3);
  }

  .preview-stat {
    text-align: center;
    flex: 1;
  }

  .stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--red-400);
  }

  .stat-label {
    display: block;
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-top: var(--space-1);
  }

  .preview-items {
    border-top: 1px solid var(--glass-border);
    padding-top: var(--space-3);
  }

  .preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2) 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  }

  .preview-item:last-child {
    border-bottom: none;
  }

  .item-name {
    color: var(--text-primary);
    font-size: 0.9rem;
  }

  .item-count {
    color: var(--text-secondary);
    font-size: 0.85rem;
  }

  .loading-preview,
  .error-preview,
  .no-reports-preview {
    text-align: center;
    padding: var(--space-4);
    color: var(--text-secondary);
  }

  .error-preview {
    color: var(--red-400);
  }

  .modal-footer {
    padding: var(--space-4);
    border-top: 1px solid var(--glass-border);
    display: flex;
    gap: var(--space-3);
    justify-content: flex-end;
  }

  /* Enhanced Filter Controls */
  .enhanced-filter-controls {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: var(--space-4);
    margin-bottom: var(--space-4);
  }

  .filter-row {
    display: flex;
    gap: var(--space-3);
    align-items: end;
    flex-wrap: wrap;
  }

  .filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
    min-width: 150px;
  }

  .filter-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-color);
    opacity: 0.9;
  }

  .filter-select {
    padding: var(--space-2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
    font-size: 0.875rem;
    transition: all 0.3s ease;
  }

  .filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
  }

  .filter-select option {
    background: var(--card-bg);
    color: var(--text-color);
  }

  /* Top Subject Performers Styles */
  .top-subject-performers-grid {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
  }

  .grade-section {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
  }

  .grade-title {
    margin: 0 0 var(--space-4) 0;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: 1.2rem;
    border-bottom: 1px solid var(--glass-border);
    padding-bottom: var(--space-2);
  }

  .streams-container {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
  }

  .stream-section {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
    padding: var(--space-3);
  }

  .stream-title {
    margin: 0 0 var(--space-3) 0;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: 1rem;
  }

  .subjects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-3);
  }

  .subject-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    padding: var(--space-3);
    transition: all 0.3s ease;
  }

  .subject-card:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--primary-500);
    transform: translateY(-2px);
  }

  .subject-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-3);
    flex-wrap: wrap;
    gap: var(--space-2);
  }

  .subject-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95rem;
  }

  .subject-stats {
    color: var(--text-secondary);
    font-size: 0.8rem;
  }

  .top-performers-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
  }

  .performer-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-2);
    background: rgba(255, 255, 255, 0.03);
    border-radius: var(--radius-sm);
    transition: all 0.3s ease;
  }

  .performer-item:hover {
    background: rgba(255, 255, 255, 0.06);
  }

  .performer-rank {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 40px;
  }

  .medal {
    font-size: 1.2rem;
    margin-bottom: var(--space-1);
  }

  .position {
    font-size: 0.7rem;
    color: var(--text-secondary);
    font-weight: 500;
  }

  .performer-info {
    flex: 1;
    min-width: 0;
  }

  .performer-name {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.85rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .performer-admission {
    color: var(--text-secondary);
    font-size: 0.75rem;
    margin-top: var(--space-1);
  }

  .performer-score {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--space-1);
    min-width: 80px;
  }

  .score-marks {
    font-size: 0.8rem;
    color: var(--text-secondary);
  }

  .score-percentage {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
  }

  .score-grade {
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-size: 0.7rem;
    font-weight: 600;
    text-align: center;
  }

  .no-performers {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: var(--space-3);
  }

  /* Responsive design for top subject performers */
  @media (max-width: 768px) {
    .subjects-grid {
      grid-template-columns: 1fr;
    }

    .subject-header {
      flex-direction: column;
      align-items: flex-start;
    }

    .performer-item {
      flex-wrap: wrap;
    }

    .performer-score {
      align-items: flex-start;
    }
  }

  /* Error state styles */
  .error-state {
    text-align: center;
    padding: var(--space-6);
    color: var(--text-secondary);
  }

  .error-actions {
    display: flex;
    justify-content: center;
    gap: var(--space-2);
    margin-top: var(--space-3);
    flex-wrap: wrap;
  }

  .error-suggestions {
    text-align: left;
    max-width: 600px;
    margin: 0 auto;
  }

  .error-suggestions h4 {
    color: var(--text-primary);
    margin-bottom: var(--space-2);
  }

  .error-suggestions ol {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
  }

  .error-suggestions li {
    margin-bottom: var(--space-1);
  }
</style>
{% endblock %}
