/**
 * HILLVIEW SCHOOL MANAGEMENT SYSTEM
 * RESPONSIVE UTILITIES JAVASCRIPT
 * 
 * This file provides JavaScript utilities to enhance responsive behavior
 * and mobile user experience across all device sizes.
 */

// ========================================
// RESPONSIVE UTILITIES CLASS
// ========================================

class ResponsiveUtils {
  constructor() {
    this.breakpoints = {
      xs: 320,
      sm: 480,
      md: 768,
      lg: 1024,
      xl: 1280,
      xxl: 1440
    };
    
    this.currentBreakpoint = this.getCurrentBreakpoint();
    this.init();
  }

  // Initialize responsive utilities
  init() {
    this.setupMobileNavigation();
    this.setupResponsiveTables();
    this.setupTouchEnhancements();
    this.setupModalEnhancements();
    this.setupFormEnhancements();
    this.handleResize();
    
    // Listen for window resize
    window.addEventListener('resize', this.debounce(() => {
      this.handleResize();
    }, 250));
  }

  // Get current breakpoint
  getCurrentBreakpoint() {
    const width = window.innerWidth;
    
    if (width < this.breakpoints.sm) return 'xs';
    if (width < this.breakpoints.md) return 'sm';
    if (width < this.breakpoints.lg) return 'md';
    if (width < this.breakpoints.xl) return 'lg';
    if (width < this.breakpoints.xxl) return 'xl';
    return 'xxl';
  }

  // Check if current viewport is mobile
  isMobile() {
    return this.currentBreakpoint === 'xs' || this.currentBreakpoint === 'sm';
  }

  // Check if current viewport is tablet
  isTablet() {
    return this.currentBreakpoint === 'md';
  }

  // Check if current viewport is desktop
  isDesktop() {
    return this.currentBreakpoint === 'lg' || this.currentBreakpoint === 'xl' || this.currentBreakpoint === 'xxl';
  }

  // Handle window resize
  handleResize() {
    const newBreakpoint = this.getCurrentBreakpoint();
    
    if (newBreakpoint !== this.currentBreakpoint) {
      this.currentBreakpoint = newBreakpoint;
      this.onBreakpointChange(newBreakpoint);
    }
    
    this.updateMobileNavigation();
    this.updateResponsiveTables();
  }

  // Callback for breakpoint changes
  onBreakpointChange(breakpoint) {
    // Dispatch custom event
    window.dispatchEvent(new CustomEvent('breakpointChange', {
      detail: { breakpoint, utils: this }
    }));
  }

  // Setup mobile navigation
  setupMobileNavigation() {
    const toggleButtons = document.querySelectorAll('.mobile-nav-toggle');
    
    toggleButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleMobileNav(button);
      });
    });

    // Close mobile nav when clicking outside
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.navbar-responsive')) {
        this.closeMobileNav();
      }
    });

    // Handle escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.closeMobileNav();
      }
    });
  }

  // Toggle mobile navigation
  toggleMobileNav(button) {
    const navbar = button.closest('.navbar-responsive');
    const nav = navbar.querySelector('.navbar-nav');
    const icon = button.querySelector('i');
    
    if (nav.classList.contains('show')) {
      nav.classList.remove('show');
      icon.className = 'fas fa-bars';
      button.setAttribute('aria-expanded', 'false');
    } else {
      nav.classList.add('show');
      icon.className = 'fas fa-times';
      button.setAttribute('aria-expanded', 'true');
    }
  }

  // Close mobile navigation
  closeMobileNav() {
    const navs = document.querySelectorAll('.navbar-nav.show');
    const toggles = document.querySelectorAll('.mobile-nav-toggle');
    
    navs.forEach(nav => nav.classList.remove('show'));
    toggles.forEach(toggle => {
      const icon = toggle.querySelector('i');
      if (icon) icon.className = 'fas fa-bars';
      toggle.setAttribute('aria-expanded', 'false');
    });
  }

  // Update mobile navigation visibility
  updateMobileNavigation() {
    const toggleButtons = document.querySelectorAll('.mobile-nav-toggle');
    
    toggleButtons.forEach(button => {
      if (this.isMobile() || this.isTablet()) {
        button.style.display = 'flex';
      } else {
        button.style.display = 'none';
        this.closeMobileNav();
      }
    });
  }

  // Setup responsive tables
  setupResponsiveTables() {
    const tables = document.querySelectorAll('.table-mobile-stack');
    
    tables.forEach(table => {
      this.enhanceTableForMobile(table);
    });
  }

  // Enhance table for mobile viewing
  enhanceTableForMobile(table) {
    const headers = table.querySelectorAll('thead th');
    const cells = table.querySelectorAll('tbody td');
    
    cells.forEach((cell, index) => {
      const headerIndex = index % headers.length;
      const headerText = headers[headerIndex]?.textContent || '';
      cell.setAttribute('data-label', headerText);
    });
  }

  // Update responsive tables
  updateResponsiveTables() {
    const tables = document.querySelectorAll('.table-responsive');
    
    tables.forEach(table => {
      if (this.isMobile()) {
        table.classList.add('table-mobile-stack');
      } else {
        table.classList.remove('table-mobile-stack');
      }
    });
  }

  // Setup touch enhancements
  setupTouchEnhancements() {
    if ('ontouchstart' in window) {
      document.body.classList.add('touch-device');
      
      // Add touch feedback to buttons
      const buttons = document.querySelectorAll('.btn, .nav-link, .card');
      buttons.forEach(button => {
        button.addEventListener('touchstart', () => {
          button.classList.add('touch-active');
        });
        
        button.addEventListener('touchend', () => {
          setTimeout(() => {
            button.classList.remove('touch-active');
          }, 150);
        });
      });
    }
  }

  // Setup modal enhancements
  setupModalEnhancements() {
    const modals = document.querySelectorAll('.modal');
    
    modals.forEach(modal => {
      // Close modal when clicking backdrop
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          this.closeModal(modal);
        }
      });
      
      // Close modal with escape key
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && modal.classList.contains('show')) {
          this.closeModal(modal);
        }
      });
      
      // Setup close buttons
      const closeButtons = modal.querySelectorAll('.modal-close, [data-dismiss="modal"]');
      closeButtons.forEach(button => {
        button.addEventListener('click', () => {
          this.closeModal(modal);
        });
      });
    });
  }

  // Open modal
  openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
      modal.classList.add('show');
      document.body.style.overflow = 'hidden';
      
      // Focus first focusable element
      const focusable = modal.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
      if (focusable) focusable.focus();
    }
  }

  // Close modal
  closeModal(modal) {
    modal.classList.remove('show');
    document.body.style.overflow = '';
  }

  // Setup form enhancements
  setupFormEnhancements() {
    const forms = document.querySelectorAll('.form-responsive');
    
    forms.forEach(form => {
      // Auto-resize textareas
      const textareas = form.querySelectorAll('.form-textarea');
      textareas.forEach(textarea => {
        this.autoResizeTextarea(textarea);
        textarea.addEventListener('input', () => {
          this.autoResizeTextarea(textarea);
        });
      });
      
      // Enhance select elements on mobile
      if (this.isMobile()) {
        const selects = form.querySelectorAll('.form-select');
        selects.forEach(select => {
          select.setAttribute('size', '1');
        });
      }
    });
  }

  // Auto-resize textarea
  autoResizeTextarea(textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = textarea.scrollHeight + 'px';
  }

  // Utility: Debounce function
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  // Utility: Show loading state
  showLoading(element) {
    element.classList.add('loading');
    element.setAttribute('aria-busy', 'true');
  }

  // Utility: Hide loading state
  hideLoading(element) {
    element.classList.remove('loading');
    element.setAttribute('aria-busy', 'false');
  }

  // Utility: Show alert
  showAlert(message, type = 'info', duration = 5000) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.innerHTML = `
      ${message}
      <button class="alert-close" aria-label="Close">
        <i class="fas fa-times"></i>
      </button>
    `;
    
    document.body.insertBefore(alert, document.body.firstChild);
    
    // Auto-remove after duration
    setTimeout(() => {
      if (alert.parentNode) {
        alert.remove();
      }
    }, duration);
    
    // Manual close
    alert.querySelector('.alert-close').addEventListener('click', () => {
      alert.remove();
    });
    
    return alert;
  }
}

// ========================================
// INITIALIZE RESPONSIVE UTILS
// ========================================

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.responsiveUtils = new ResponsiveUtils();
  });
} else {
  window.responsiveUtils = new ResponsiveUtils();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ResponsiveUtils;
}
