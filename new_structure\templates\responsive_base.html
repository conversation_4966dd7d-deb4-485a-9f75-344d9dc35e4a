<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, maximum-scale=5.0">
    <meta name="description" content="{% block meta_description %}Hillview School Management System - Comprehensive school administration platform{% endblock %}">
    <meta name="keywords" content="{% block meta_keywords %}school management, education, student records, teacher portal, academic management{% endblock %}">
    <meta name="author" content="Hillview School Management System">
    
    <!-- Responsive and Mobile Optimization -->
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#007bff">
    
    <title>{% block title %}Hillview School Management System{% endblock %}</title>
    
    <!-- Preconnect to external resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">
    
    <!-- Core CSS Framework -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/responsive_framework.css') }}">
    
    <!-- Page-specific CSS -->
    {% block extra_css %}{% endblock %}
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="apple-touch-icon" href="{{ url_for('static', filename='apple-touch-icon.png') }}">
    
    <!-- Critical CSS for above-the-fold content -->
    <style>
        /* Critical CSS - Inline for performance */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.3s ease;
        }
        
        .loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="{% block body_class %}{% endblock %}">
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="spinner"></div>
    </div>
    
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only">Skip to main content</a>
    
    <!-- Navigation -->
    {% block navigation %}
    <nav class="navbar-responsive" role="navigation" aria-label="Main navigation">
        <div class="container">
            <a href="{% block home_url %}{{ url_for('auth.login') }}{% endblock %}" class="navbar-brand">
                {% if school_info and school_info.logo_filename %}
                <img src="{{ url_for('static', filename='uploads/logos/' + school_info.logo_filename) }}" 
                     alt="{{ school_info.school_name or 'School' }} Logo" 
                     class="img-responsive" 
                     style="height: 32px; width: auto;">
                {% else %}
                <i class="fas fa-graduation-cap"></i>
                {% endif %}
                <span>{{ school_info.school_name or 'Hillview School' }}</span>
            </a>
            
            <button class="mobile-nav-toggle" 
                    aria-label="Toggle navigation menu" 
                    aria-expanded="false"
                    aria-controls="main-navigation">
                <i class="fas fa-bars"></i>
            </button>
            
            <ul class="navbar-nav" id="main-navigation">
                {% block nav_items %}
                <li><a href="{{ url_for('auth.login') }}" class="nav-link">Home</a></li>
                {% endblock %}
            </ul>
        </div>
    </nav>
    {% endblock %}
    
    <!-- Main Content -->
    <main id="main-content" class="main-content" role="main">
        <div class="container">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <div class="alerts-container" role="alert" aria-live="polite">
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'error' if category == 'error' else 'success' if category == 'success' else 'warning' if category == 'warning' else 'info' }}">
                                {{ message }}
                                <button class="alert-close" aria-label="Close alert">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endwith %}
            
            <!-- Page Header -->
            {% block page_header %}
            <header class="page-header">
                <div class="header-content">
                    <div>
                        <h1 class="header-title">{% block page_title %}Page Title{% endblock %}</h1>
                        {% block page_subtitle %}{% endblock %}
                    </div>
                    <div class="header-actions">
                        {% block header_actions %}{% endblock %}
                    </div>
                </div>
            </header>
            {% endblock %}
            
            <!-- Breadcrumb Navigation -->
            {% block breadcrumb %}
            <nav class="breadcrumb-nav" aria-label="Breadcrumb">
                <ol class="breadcrumb">
                    <li><a href="{% block breadcrumb_home %}{{ url_for('auth.login') }}{% endblock %}">
                        <i class="fas fa-home"></i> Home
                    </a></li>
                    {% block breadcrumb_items %}{% endblock %}
                    <li class="current" aria-current="page">{% block breadcrumb_current %}Current Page{% endblock %}</li>
                </ol>
            </nav>
            {% endblock %}
            
            <!-- Page Content -->
            <div class="page-content">
                {% block content %}
                <p>Page content goes here.</p>
                {% endblock %}
            </div>
        </div>
    </main>
    
    <!-- Footer -->
    {% block footer %}
    <footer class="site-footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>{{ school_info.school_name or 'Hillview School' }}</h3>
                    <p>Comprehensive School Management System</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="{{ url_for('auth.login') }}">Login</a></li>
                        {% block footer_links %}{% endblock %}
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>
                        {% if school_info and school_info.email %}
                            <i class="fas fa-envelope"></i> {{ school_info.email }}
                        {% endif %}
                    </p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; {{ moment().format('YYYY') }} {{ school_info.school_name or 'Hillview School' }}. All rights reserved.</p>
            </div>
        </div>
    </footer>
    {% endblock %}
    
    <!-- Core JavaScript -->
    <script src="{{ url_for('static', filename='js/responsive_utils.js') }}"></script>
    
    <!-- Page-specific JavaScript -->
    {% block extra_js %}{% endblock %}
    
    <!-- Initialize page -->
    <script>
        // Hide loading screen when page is fully loaded
        window.addEventListener('load', function() {
            const loadingScreen = document.getElementById('loadingScreen');
            if (loadingScreen) {
                loadingScreen.classList.add('hidden');
                setTimeout(() => {
                    loadingScreen.remove();
                }, 300);
            }
        });
        
        // Handle alert close buttons
        document.addEventListener('click', function(e) {
            if (e.target.closest('.alert-close')) {
                const alert = e.target.closest('.alert');
                if (alert) {
                    alert.style.opacity = '0';
                    alert.style.transform = 'translateY(-10px)';
                    setTimeout(() => alert.remove(), 300);
                }
            }
        });
        
        // Auto-hide alerts after 5 seconds
        document.querySelectorAll('.alert').forEach(alert => {
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.style.opacity = '0';
                    alert.style.transform = 'translateY(-10px)';
                    setTimeout(() => alert.remove(), 300);
                }
            }, 5000);
        });
        
        // Page-specific initialization
        {% block page_js %}{% endblock %}
    </script>
    
    <!-- Service Worker for PWA capabilities (optional) -->
    {% if config.PWA_ENABLED %}
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
    {% endif %}
</body>
</html>
