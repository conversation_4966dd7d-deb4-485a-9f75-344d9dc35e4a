<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <title>
      Student Promotion Management - {{ school_info.school_name or 'Hillview
      School' }}
    </title>

    <!-- Modern Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Icons -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />

    <!-- Modern CSS Framework -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/modern_classteacher.css') }}"
    />

    <style>
      /* Student Promotion Specific Styles */
      :root {
        --promotion-primary: #667eea;
        --promotion-secondary: #764ba2;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --info-color: #3b82f6;
      }

      .page-container {
        background: linear-gradient(
          135deg,
          var(--promotion-primary) 0%,
          var(--promotion-secondary) 100%
        );
        min-height: 100vh;
        padding: var(--space-6);
      }

      .promotion-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: var(--radius-2xl);
        padding: var(--space-8);
        margin-bottom: var(--space-6);
        box-shadow: var(--shadow-xl);
      }

      .promotion-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: var(--space-4);
        margin-bottom: var(--space-6);
      }

      .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border-radius: var(--radius-xl);
        padding: var(--space-6);
        text-align: center;
        box-shadow: var(--shadow-lg);
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
      }

      .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--promotion-primary), var(--promotion-secondary));
      }

      .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
        background: rgba(255, 255, 255, 1);
      }

      .stat-number {
        font-size: 2.8rem;
        font-weight: 800;
        color: var(--promotion-primary);
        margin-bottom: var(--space-2);
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .stat-label {
        font-size: 0.9rem;
        color: var(--text-secondary);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .stat-icon {
        font-size: 2rem;
        color: var(--promotion-secondary);
        margin-bottom: var(--space-3);
        opacity: 0.8;
      }

      /* Advanced Analytics Section */
      .analytics-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: var(--radius-2xl);
        padding: var(--space-6);
        margin-bottom: var(--space-6);
        box-shadow: var(--shadow-xl);
      }

      .analytics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--space-6);
        margin-top: var(--space-4);
      }

      .chart-container {
        background: rgba(255, 255, 255, 0.8);
        border-radius: var(--radius-lg);
        padding: var(--space-4);
        box-shadow: var(--shadow-md);
        min-height: 250px;
      }

      .chart-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--space-3);
        text-align: center;
      }

      /* Batch Processing Controls */
      .batch-controls {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: var(--radius-2xl);
        padding: var(--space-6);
        margin-bottom: var(--space-6);
        box-shadow: var(--shadow-xl);
      }

      .batch-actions {
        display: flex;
        gap: var(--space-3);
        flex-wrap: wrap;
        margin-top: var(--space-4);
      }

      .batch-btn {
        padding: var(--space-3) var(--space-5);
        border-radius: var(--radius-lg);
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: var(--space-2);
        font-size: 0.9rem;
      }

      .batch-btn-primary {
        background: linear-gradient(135deg, var(--promotion-primary), var(--promotion-secondary));
        color: white;
      }

      .batch-btn-success {
        background: linear-gradient(135deg, var(--success-color), #059669);
        color: white;
      }

      .batch-btn-warning {
        background: linear-gradient(135deg, var(--warning-color), #d97706);
        color: white;
      }

      .batch-btn-danger {
        background: linear-gradient(135deg, var(--danger-color), #dc2626);
        color: white;
      }

      .batch-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
      }

      .promotion-controls {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: var(--radius-2xl);
        padding: var(--space-6);
        margin-bottom: var(--space-6);
        box-shadow: var(--shadow-xl);
      }

      /* Filter Controls */
      .filter-form {
        margin-top: var(--space-4);
      }

      .filter-row {
        display: flex;
        flex-wrap: wrap;
        gap: var(--space-4);
        align-items: end;
      }

      .filter-item {
        display: flex;
        flex-direction: column;
        min-width: 150px;
      }

      .filter-item label {
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: var(--space-2);
        font-size: 0.9rem;
      }

      .filter-item select {
        padding: var(--space-2) var(--space-3);
        border: 2px solid rgba(103, 126, 234, 0.2);
        border-radius: var(--radius-lg);
        background: white;
        color: var(--text-primary);
        font-size: 0.9rem;
        transition: all 0.3s ease;
      }

      .filter-item select:focus {
        outline: none;
        border-color: var(--promotion-primary);
        box-shadow: 0 0 0 3px rgba(103, 126, 234, 0.1);
      }

      .btn-filter {
        padding: var(--space-2) var(--space-4);
        border: none;
        border-radius: var(--radius-lg);
        background: var(--promotion-primary);
        color: white;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: var(--space-2);
        font-size: 0.9rem;
      }

      .btn-filter:hover {
        background: var(--promotion-secondary);
        transform: translateY(-2px);
      }

      .btn-clear {
        background: var(--warning-color) !important;
        text-decoration: none;
      }

      .btn-clear:hover {
        background: #d97706 !important;
      }

      /* Pagination Controls */
      .pagination-controls {
        display: flex;
        align-items: center;
        gap: var(--space-4);
      }

      .pagination-info {
        flex: 1;
      }

      .btn-pagination {
        padding: var(--space-2) var(--space-4);
        border: 2px solid var(--promotion-primary);
        border-radius: var(--radius-lg);
        background: white;
        color: var(--promotion-primary);
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: var(--space-2);
      }

      .btn-pagination:hover {
        background: var(--promotion-primary);
        color: white;
        transform: translateY(-2px);
      }

      .page-info {
        padding: var(--space-2) var(--space-4);
        background: rgba(103, 126, 234, 0.1);
        border-radius: var(--radius-lg);
        color: var(--promotion-primary);
        font-weight: 600;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .filter-row {
          flex-direction: column;
          align-items: stretch;
        }

        .filter-item {
          min-width: auto;
        }

        .pagination-controls {
          flex-direction: column;
          gap: var(--space-2);
        }

        .pagination-info {
          text-align: center;
        }
      }

      .control-group {
        display: flex;
        gap: var(--space-4);
        align-items: center;
        flex-wrap: wrap;
      }

      .academic-year-selector {
        display: flex;
        align-items: center;
        gap: var(--space-2);
      }

      .academic-year-selector select {
        padding: var(--space-3);
        border-radius: var(--radius-lg);
        border: 2px solid var(--border-color);
        background: white;
        font-weight: 500;
      }

      .promotion-actions {
        display: flex;
        gap: var(--space-3);
      }

      .btn-promotion {
        padding: var(--space-3) var(--space-6);
        border-radius: var(--radius-lg);
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: var(--space-2);
      }

      .btn-primary {
        background: var(--promotion-primary);
        color: white;
      }

      .btn-primary:hover {
        background: var(--promotion-secondary);
        transform: translateY(-1px);
      }

      .btn-success {
        background: var(--success-color);
        color: white;
      }

      .btn-warning {
        background: var(--warning-color);
        color: white;
      }

      .grade-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: var(--radius-2xl);
        margin-bottom: var(--space-6);
        box-shadow: var(--shadow-xl);
        overflow: hidden;
      }

      .grade-header {
        background: linear-gradient(
          135deg,
          var(--promotion-primary),
          var(--promotion-secondary)
        );
        color: white;
        padding: var(--space-6);
        display: flex;
        justify-content: between;
        align-items: center;
      }

      .grade-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin: 0;
      }

      .grade-summary {
        display: flex;
        gap: var(--space-4);
        font-size: 0.875rem;
      }

      .students-table {
        width: 100%;
        border-collapse: collapse;
      }

      .students-table th,
      .students-table td {
        padding: var(--space-4);
        text-align: left;
        border-bottom: 1px solid var(--border-color);
      }

      .students-table th {
        background: rgba(var(--promotion-primary-rgb), 0.1);
        font-weight: 600;
        color: var(--text-primary);
      }

      .students-table tr:hover {
        background: rgba(var(--promotion-primary-rgb), 0.05);
      }

      .student-checkbox {
        width: 18px;
        height: 18px;
        cursor: pointer;
      }

      .promotion-select {
        padding: var(--space-2);
        border-radius: var(--radius-md);
        border: 1px solid var(--border-color);
        background: white;
        min-width: 120px;
      }

      .stream-select {
        padding: var(--space-2);
        border-radius: var(--radius-md);
        border: 1px solid var(--border-color);
        background: white;
        min-width: 100px;
      }

      .notes-input {
        padding: var(--space-2);
        border-radius: var(--radius-md);
        border: 1px solid var(--border-color);
        background: white;
        min-width: 150px;
        font-size: 0.875rem;
      }

      .status-badge {
        padding: var(--space-1) var(--space-3);
        border-radius: var(--radius-full);
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
      }

      .status-eligible {
        background: rgba(16, 185, 129, 0.1);
        color: var(--success-color);
      }

      .status-final {
        background: rgba(245, 158, 11, 0.1);
        color: var(--warning-color);
      }

      .status-inactive {
        background: rgba(107, 114, 128, 0.1);
        color: var(--text-secondary);
      }

      .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 1000;
      }

      .loading-spinner {
        background: white;
        padding: var(--space-8);
        border-radius: var(--radius-2xl);
        text-align: center;
      }

      .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid var(--border-color);
        border-top: 4px solid var(--promotion-primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto var(--space-4);
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      /* Mobile Responsive */
      @media (max-width: 768px) {
        .page-container {
          padding: var(--space-4);
        }

        .promotion-header {
          padding: var(--space-6);
        }

        .promotion-stats {
          grid-template-columns: repeat(2, 1fr);
        }

        .control-group {
          flex-direction: column;
          align-items: stretch;
        }

        .promotion-actions {
          justify-content: center;
        }

        .students-table {
          font-size: 0.875rem;
        }

        .students-table th,
        .students-table td {
          padding: var(--space-2);
        }
      }
    </style>
  </head>
  <body>
    <div class="page-container">
      <div class="content-wrapper">
        <!-- Header -->
        <div class="promotion-header">
          <div
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: var(--space-4);
            "
          >
            <div>
              <h1
                style="
                  margin: 0;
                  color: var(--text-primary);
                  font-size: 2rem;
                  font-weight: 800;
                "
              >
                <i
                  class="fas fa-graduation-cap"
                  style="
                    color: var(--promotion-primary);
                    margin-right: var(--space-3);
                  "
                ></i>
                Student Promotion Management
              </h1>
              <p
                style="
                  margin: var(--space-2) 0 0;
                  color: var(--text-secondary);
                  font-size: 1rem;
                "
              >
                Manage student promotions for Academic Year {{
                promotion_data.academic_year or '2024' }}
              </p>
            </div>
            <div style="display: flex; gap: var(--space-3);">
              <a
                href="{{ url_for('admin.dashboard') }}"
                class="btn-promotion btn-primary"
              >
                <i class="fas fa-arrow-left"></i>
                Back to Dashboard
              </a>
              <a
                href="{{ url_for('admin.create_sample_promotion_data') }}"
                class="btn-promotion"
                style="background: linear-gradient(135deg, var(--success-color), #059669); color: white;"
                onclick="return confirm('This will create sample students for testing. Continue?')"
              >
                <i class="fas fa-database"></i>
                Create Sample Data
              </a>
            </div>
          </div>

          <!-- Enhanced Promotion Statistics -->
          <div class="promotion-stats">
            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-users"></i>
              </div>
              <div class="stat-number">
                {{ promotion_data.promotion_summary.total_students or 0 }}
              </div>
              <div class="stat-label">Total Students</div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-arrow-up"></i>
              </div>
              <div class="stat-number">
                {{ promotion_data.promotion_summary.eligible_for_promotion or 0 }}
              </div>
              <div class="stat-label">Eligible for Promotion</div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-graduation-cap"></i>
              </div>
              <div class="stat-number">
                {{ promotion_data.promotion_summary.final_grade_students or 0 }}
              </div>
              <div class="stat-label">Final Grade (Grade 9)</div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-layer-group"></i>
              </div>
              <div class="stat-number">
                {{ promotion_data.students_by_grade|length or 0 }}
              </div>
              <div class="stat-label">Grade Levels</div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-redo"></i>
              </div>
              <div class="stat-number" id="repeat-count">0</div>
              <div class="stat-label">To Repeat</div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-exchange-alt"></i>
              </div>
              <div class="stat-number" id="transfer-count">0</div>
              <div class="stat-label">To Transfer</div>
            </div>
          </div>
        </div>

        <!-- Advanced Analytics Section -->
        <div class="analytics-section">
          <h3 style="color: var(--text-primary); margin-bottom: var(--space-4); display: flex; align-items: center; gap: var(--space-2);">
            <i class="fas fa-chart-bar"></i> Promotion Analytics
          </h3>
          <div class="analytics-grid">
            <div class="chart-container">
              <div class="chart-title">Grade Distribution</div>
              <canvas id="gradeChart" width="300" height="200"></canvas>
            </div>
            <div class="chart-container">
              <div class="chart-title">Promotion Actions</div>
              <canvas id="actionChart" width="300" height="200"></canvas>
            </div>
            <div class="chart-container">
              <div class="chart-title">Performance Trends</div>
              <canvas id="performanceChart" width="300" height="200"></canvas>
            </div>
          </div>
        </div>

        <!-- Batch Processing Controls -->
        <div class="batch-controls">
          <h3 style="color: var(--text-primary); margin-bottom: var(--space-4); display: flex; align-items: center; gap: var(--space-2);">
            <i class="fas fa-tasks"></i> Batch Processing
          </h3>
          <div class="batch-actions">
            <button class="batch-btn batch-btn-primary" onclick="selectAllEligible()">
              <i class="fas fa-check-double"></i>
              Select All Eligible
            </button>
            <button class="batch-btn batch-btn-success" onclick="promoteSelected()">
              <i class="fas fa-arrow-up"></i>
              Promote Selected
            </button>
            <button class="batch-btn batch-btn-warning" onclick="repeatSelected()">
              <i class="fas fa-redo"></i>
              Repeat Selected
            </button>
            <button class="batch-btn batch-btn-danger" onclick="clearSelection()">
              <i class="fas fa-times"></i>
              Clear Selection
            </button>
            <button class="batch-btn batch-btn-primary" onclick="exportPromotionReport()">
              <i class="fas fa-download"></i>
              Export Report
            </button>
            <button class="batch-btn batch-btn-primary" onclick="previewPromotions()">
              <i class="fas fa-eye"></i>
              Preview Changes
            </button>
          </div>
          <div style="margin-top: var(--space-4); padding: var(--space-3); background: rgba(59, 130, 246, 0.1); border-radius: var(--radius-lg); border-left: 4px solid var(--info-color);">
            <p style="margin: 0; color: var(--info-color); font-weight: 500;">
              <i class="fas fa-info-circle"></i>
              Selected: <span id="selected-count">0</span> students |
              Ready for promotion: <span id="promotion-ready">0</span> |
              Requires review: <span id="review-required">0</span>
            </p>
          </div>
        </div>

        <!-- Filter Controls -->
        <div class="promotion-controls">
          <div class="control-group">
            <h3 style="color: var(--text-primary); margin-bottom: var(--space-4);">
              <i class="fas fa-filter"></i> Filter Students
            </h3>
            <form method="GET" action="{{ url_for('admin.student_promotion') }}" class="filter-form">
              <div class="filter-row">
                <div class="filter-item">
                  <label for="education_level">Education Level:</label>
                  <select id="education_level" name="education_level" onchange="this.form.submit()">
                    <option value="">All Levels</option>
                    {% for level in filter_options.education_levels %}
                    <option value="{{ level }}" {% if promotion_data.filters.education_level == level %}selected{% endif %}>
                      {{ level }}
                    </option>
                    {% endfor %}
                  </select>
                </div>

                <div class="filter-item">
                  <label for="grade_id">Grade:</label>
                  <select id="grade_id" name="grade_id" onchange="this.form.submit()">
                    <option value="">All Grades</option>
                    {% for grade in filter_options.grades %}
                    <option value="{{ grade.id }}" {% if promotion_data.filters.grade_id == grade.id %}selected{% endif %}>
                      {{ grade.name }}
                    </option>
                    {% endfor %}
                  </select>
                </div>

                <div class="filter-item">
                  <label for="stream_id">Stream:</label>
                  <select id="stream_id" name="stream_id" onchange="this.form.submit()">
                    <option value="">All Streams</option>
                    {% for stream in filter_options.streams %}
                    <option value="{{ stream.id }}" {% if promotion_data.filters.stream_id == stream.id %}selected{% endif %}>
                      {{ stream.name }} ({{ stream.grade_name }})
                    </option>
                    {% endfor %}
                  </select>
                </div>

                <div class="filter-item">
                  <label for="per_page">Per Page:</label>
                  <select id="per_page" name="per_page" onchange="this.form.submit()">
                    <option value="25" {% if promotion_data.pagination.per_page == 25 %}selected{% endif %}>25</option>
                    <option value="50" {% if promotion_data.pagination.per_page == 50 %}selected{% endif %}>50</option>
                    <option value="100" {% if promotion_data.pagination.per_page == 100 %}selected{% endif %}>100</option>
                  </select>
                </div>

                <div class="filter-item">
                  <button type="submit" class="btn-filter">
                    <i class="fas fa-search"></i> Apply Filters
                  </button>
                  <a href="{{ url_for('admin.student_promotion') }}" class="btn-filter btn-clear">
                    <i class="fas fa-times"></i> Clear
                  </a>
                </div>
              </div>

              <!-- Preserve current page when filtering -->
              <input type="hidden" name="page" value="1">
            </form>
          </div>
        </div>

        <!-- Promotion Controls -->
        <div class="promotion-controls">
          <div class="control-group">
            <div class="academic-year-selector">
              <label
                for="academic-year"
                style="font-weight: 600; color: var(--text-primary)"
              >
                Promote to Academic Year:
              </label>
              <select id="academic-year" name="academic_year_to">
                <option value="2025">2025</option>
                <option value="2026">2026</option>
                <option value="2027">2027</option>
              </select>
            </div>

            <div class="promotion-actions">
              <button
                type="button"
                class="btn-promotion btn-primary"
                onclick="selectAllEligible()"
              >
                <i class="fas fa-check-double"></i>
                Select All Eligible
              </button>
              <button
                type="button"
                class="btn-promotion btn-warning"
                onclick="clearAllSelections()"
              >
                <i class="fas fa-times"></i>
                Clear Selections
              </button>
              <button
                type="button"
                class="btn-promotion btn-success"
                onclick="processPromotions()"
              >
                <i class="fas fa-rocket"></i>
                Process Promotions
              </button>
            </div>
          </div>
        </div>

        <!-- Students by Grade -->
        {% if promotion_data.success and promotion_data.students_by_grade %} {%
        for grade_name, grade_data in promotion_data.students_by_grade.items()
        %}
        <div class="grade-section">
          <div class="grade-header">
            <div>
              <h2 class="grade-title">{{ grade_name }}</h2>
            </div>
            <div class="grade-summary">
              <span
                ><i class="fas fa-users"></i> {{ grade_data.total_count }}
                students</span
              >
              <span
                ><i class="fas fa-arrow-up"></i> {{ grade_data.eligible_count }}
                eligible</span
              >
              {% if grade_data.final_grade_count > 0 %}
              <span
                ><i class="fas fa-graduation-cap"></i> {{
                grade_data.final_grade_count }} graduating</span
              >
              {% endif %}
            </div>
          </div>

          <div style="padding: var(--space-6)">
            <table class="students-table">
              <thead>
                <tr>
                  <th style="width: 50px">
                    <input
                      type="checkbox"
                      class="student-checkbox grade-select-all"
                      data-grade="{{ grade_name }}"
                      onchange="toggleGradeSelection('{{ grade_name }}')"
                    />
                  </th>
                  <th>Student Name</th>
                  <th>Admission No.</th>
                  <th>Current Stream</th>
                  <th>Status</th>
                  <th>Action</th>
                  <th>Target Grade</th>
                  <th>Target Stream</th>
                  <th>Notes</th>
                </tr>
              </thead>
              <tbody>
                {% for student in grade_data.students %}
                <tr
                  data-student-id="{{ student.id }}"
                  data-grade="{{ grade_name }}"
                >
                  <td>
                    <input
                      type="checkbox"
                      class="student-checkbox student-select"
                      data-student-id="{{ student.id }}"
                      data-grade="{{ grade_name }}"
                      {%
                      if
                      student.can_be_promoted
                      %}checked{%
                      endif
                      %}
                      onchange="updateStudentSelection({{ student.id }})"
                    />
                  </td>
                  <td style="font-weight: 600">{{ student.name }}</td>
                  <td>{{ student.admission_number }}</td>
                  <td>{{ student.stream_name or 'No Stream' }}</td>
                  <td>
                    {% if student.is_final_grade %}
                    <span class="status-badge status-final">Final Grade</span>
                    {% elif student.can_be_promoted %}
                    <span class="status-badge status-eligible">Eligible</span>
                    {% else %}
                    <span class="status-badge status-inactive"
                      >Not Eligible</span
                    >
                    {% endif %}
                  </td>
                  <td>
                    <select
                      class="promotion-select"
                      data-student-id="{{ student.id }}"
                      onchange="updatePromotionAction({{ student.id }})"
                    >
                      {% if student.is_final_grade %}
                      <option value="graduate" selected>Graduate</option>
                      <option value="repeat">Repeat Grade</option>
                      <option value="transfer">Transfer</option>
                      {% else %}
                      <option
                        value="promote"
                        {%
                        if
                        student.can_be_promoted
                        %}selected{%
                        endif
                        %}
                      >
                        Promote
                      </option>
                      <option value="repeat">Repeat Grade</option>
                      <option value="transfer">Transfer</option>
                      {% endif %}
                    </select>
                  </td>
                  <td>
                    <span
                      class="target-grade"
                      data-student-id="{{ student.id }}"
                    >
                      {% if student.next_grade and not student.is_final_grade %}
                      {{ student.next_grade }} {% else %} N/A {% endif %}
                    </span>
                  </td>
                  <td>
                    <select
                      class="stream-select"
                      data-student-id="{{ student.id }}"
                      style="{% if student.is_final_grade %}display: none;{% endif %}"
                    >
                      <option value="">Select Stream</option>
                      <!-- Streams will be populated by JavaScript -->
                    </select>
                  </td>
                  <td>
                    <input
                      type="text"
                      class="notes-input"
                      data-student-id="{{ student.id }}"
                      placeholder="Optional notes..."
                    />
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
        {% endfor %}

        <!-- Pagination Controls -->
        {% if promotion_data.pagination and promotion_data.pagination.total_pages > 1 %}
        <div class="promotion-controls">
          <div class="control-group">
            <div class="pagination-info">
              <p style="color: var(--text-primary); margin: 0;">
                Showing {{ ((promotion_data.pagination.page - 1) * promotion_data.pagination.per_page) + 1 }}
                to {{ [promotion_data.pagination.page * promotion_data.pagination.per_page, promotion_data.pagination.total] | min }}
                of {{ promotion_data.pagination.total }} students
              </p>
            </div>

            <div class="pagination-controls">
              {% if promotion_data.pagination.has_prev %}
              <a href="{{ url_for('admin.student_promotion',
                        page=promotion_data.pagination.prev_num,
                        education_level=promotion_data.filters.education_level,
                        grade_id=promotion_data.filters.grade_id,
                        stream_id=promotion_data.filters.stream_id,
                        per_page=promotion_data.pagination.per_page) }}"
                 class="btn-pagination">
                <i class="fas fa-chevron-left"></i> Previous
              </a>
              {% endif %}

              <span class="page-info">
                Page {{ promotion_data.pagination.page }} of {{ promotion_data.pagination.total_pages }}
              </span>

              {% if promotion_data.pagination.has_next %}
              <a href="{{ url_for('admin.student_promotion',
                        page=promotion_data.pagination.next_num,
                        education_level=promotion_data.filters.education_level,
                        grade_id=promotion_data.filters.grade_id,
                        stream_id=promotion_data.filters.stream_id,
                        per_page=promotion_data.pagination.per_page) }}"
                 class="btn-pagination">
                Next <i class="fas fa-chevron-right"></i>
              </a>
              {% endif %}
            </div>
          </div>
        </div>
        {% endif %}

        {% else %}
        <div class="grade-section">
          <div style="padding: var(--space-8); text-align: center">
            <i
              class="fas fa-exclamation-triangle"
              style="
                font-size: 3rem;
                color: var(--warning-color);
                margin-bottom: var(--space-4);
              "
            ></i>
            <h3
              style="color: var(--text-primary); margin-bottom: var(--space-2)"
            >
              No Students Found
            </h3>
            <div style="color: var(--text-secondary); line-height: 1.6;">
              {% if promotion_data.error %}
              <p style="margin-bottom: var(--space-4); color: var(--error-color);">
                <strong>Error:</strong> {{ promotion_data.error }}
              </p>
              {% else %}
              <p style="margin-bottom: var(--space-4);">
                No active students available for promotion with the current filters.
              </p>
              <div style="background: rgba(255, 193, 7, 0.1); padding: var(--space-4); border-radius: var(--radius-lg); margin: var(--space-4) 0; text-align: left;">
                <p style="margin: 0; color: #856404; font-weight: 600;">Possible reasons:</p>
                <ul style="margin: var(--space-2) 0 0 var(--space-4); color: #856404;">
                  <li>No students have been added to the system yet</li>
                  <li>Students don't have grade assignments</li>
                  <li>All students are in Grade 9 (final grade - no promotion needed)</li>
                  <li>Current filters are too restrictive</li>
                </ul>
              </div>
              <div style="margin-top: var(--space-6);">
                <a href="{{ url_for('admin.student_promotion') }}" class="btn-clear" style="text-decoration: none; display: inline-block; margin-right: var(--space-2);">
                  <i class="fas fa-refresh"></i> Clear All Filters
                </a>
                <a href="{{ url_for('classteacher.manage_students') }}" class="btn-filter" style="text-decoration: none; display: inline-block;">
                  <i class="fas fa-users"></i> Manage Students
                </a>
              </div>
              {% endif %}
            </div>
          </div>
        </div>
        {% endif %}
      </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
      <div class="loading-spinner">
        <div class="spinner"></div>
        <h3 style="margin: 0; color: var(--text-primary)">
          Processing Promotions...
        </h3>
        <p style="margin: var(--space-2) 0 0; color: var(--text-secondary)">
          Please wait while we update student records.
        </p>
      </div>
    </div>

    <!-- Chart.js for Analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
      // Only execute if we're on the student promotion page
      if (document.querySelector('.student-promotion-container') || document.querySelector('.promotion-dashboard')) {
        // Global variables with error handling
        let promotionData;
        try {
          {% if promotion_data %}
          promotionData = {{ promotion_data | tojson }};
          {% else %}
          promotionData = { students_by_grade: {}, available_streams: {}, pagination: { page: 1, total_pages: 1 } };
          {% endif %}

          if (!promotionData || typeof promotionData !== 'object') {
            console.warn('Invalid promotion data structure, using defaults');
            promotionData = { students_by_grade: {}, available_streams: {}, pagination: { page: 1, total_pages: 1 } };
          }
        } catch (error) {
          console.warn('Error loading student promotion data, using defaults:', error.message);
          promotionData = { students_by_grade: {}, available_streams: {}, pagination: { page: 1, total_pages: 1 } };
        }

      let selectedStudents = new Map();
      let availableStreams = promotionData.available_streams || {};
      let charts = {};

      // Initialize page
      document.addEventListener('DOMContentLoaded', function() {
          initializeStreamSelectors();
          initializeSelectedStudents();
          initializeCharts();
          updateStatistics();
      });

      // Enhanced Analytics Functions
      function initializeCharts() {
          // Grade Distribution Chart
          const gradeCtx = document.getElementById('gradeChart');
          if (gradeCtx) {
              const gradeData = Object.keys(promotionData.students_by_grade || {}).map(grade => ({
                  grade: grade,
                  count: promotionData.students_by_grade[grade].students.length
              }));

              charts.gradeChart = new Chart(gradeCtx, {
                  type: 'doughnut',
                  data: {
                      labels: gradeData.map(d => `Grade ${d.grade}`),
                      datasets: [{
                          data: gradeData.map(d => d.count),
                          backgroundColor: [
                              '#667eea', '#764ba2', '#10b981', '#f59e0b',
                              '#ef4444', '#3b82f6', '#8b5cf6', '#06b6d4'
                          ],
                          borderWidth: 2,
                          borderColor: '#ffffff'
                      }]
                  },
                  options: {
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                          legend: {
                              position: 'bottom',
                              labels: { padding: 15, usePointStyle: true }
                          }
                      }
                  }
              });
          }

          // Promotion Actions Chart
          const actionCtx = document.getElementById('actionChart');
          if (actionCtx) {
              charts.actionChart = new Chart(actionCtx, {
                  type: 'bar',
                  data: {
                      labels: ['Promote', 'Repeat', 'Transfer', 'Graduate'],
                      datasets: [{
                          label: 'Students',
                          data: [0, 0, 0, 0],
                          backgroundColor: ['#10b981', '#f59e0b', '#3b82f6', '#667eea'],
                          borderRadius: 8,
                          borderSkipped: false
                      }]
                  },
                  options: {
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                          legend: { display: false }
                      },
                      scales: {
                          y: { beginAtZero: true, ticks: { stepSize: 1 } }
                      }
                  }
              });
          }

          // Performance Trends Chart
          const performanceCtx = document.getElementById('performanceChart');
          if (performanceCtx) {
              charts.performanceChart = new Chart(performanceCtx, {
                  type: 'line',
                  data: {
                      labels: ['Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9'],
                      datasets: [{
                          label: 'Promotion Rate (%)',
                          data: [95, 92, 89, 87, 85, 83, 80, 78, 100],
                          borderColor: '#667eea',
                          backgroundColor: 'rgba(102, 126, 234, 0.1)',
                          fill: true,
                          tension: 0.4
                      }]
                  },
                  options: {
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                          legend: { display: false }
                      },
                      scales: {
                          y: { beginAtZero: true, max: 100, ticks: { callback: value => value + '%' } }
                      }
                  }
              });
          }
      }

      // Batch Processing Functions
      function selectAllEligible() {
          document.querySelectorAll('.student-select').forEach(checkbox => {
              const studentRow = checkbox.closest('tr');
              const statusBadge = studentRow.querySelector('.status-badge');
              if (statusBadge && (statusBadge.classList.contains('status-eligible') || statusBadge.classList.contains('status-final'))) {
                  checkbox.checked = true;
                  updateStudentSelection(parseInt(checkbox.dataset.studentId));
              }
          });
          updateStatistics();
      }

      function promoteSelected() {
          selectedStudents.forEach((studentData, studentId) => {
              const actionSelect = document.querySelector(`.promotion-select[data-student-id="${studentId}"]`);
              if (actionSelect && !studentData.is_final_grade) {
                  actionSelect.value = 'promote';
                  updatePromotionAction(studentId);
              }
          });
          updateStatistics();
      }

      function repeatSelected() {
          selectedStudents.forEach((studentData, studentId) => {
              const actionSelect = document.querySelector(`.promotion-select[data-student-id="${studentId}"]`);
              if (actionSelect) {
                  actionSelect.value = 'repeat';
                  updatePromotionAction(studentId);
              }
          });
          updateStatistics();
      }

      function clearSelection() {
          document.querySelectorAll('.student-select').forEach(checkbox => {
              checkbox.checked = false;
          });
          document.querySelectorAll('.grade-select-all').forEach(checkbox => {
              checkbox.checked = false;
          });
          selectedStudents.clear();
          updateStatistics();
      }

      function previewPromotions() {
          if (selectedStudents.size === 0) {
              alert('Please select at least one student to preview promotions.');
              return;
          }

          let previewText = 'PROMOTION PREVIEW\\n\\n';
          let promoteCount = 0, repeatCount = 0, transferCount = 0, graduateCount = 0;

          selectedStudents.forEach((studentData, studentId) => {
              const actionSelect = document.querySelector(`.promotion-select[data-student-id="${studentId}"]`);
              const action = actionSelect ? actionSelect.value : 'promote';

              previewText += `${studentData.name} (${studentData.admission_number}) - ${action.toUpperCase()}\\n`;

              switch(action) {
                  case 'promote': promoteCount++; break;
                  case 'repeat': repeatCount++; break;
                  case 'transfer': transferCount++; break;
                  case 'graduate': graduateCount++; break;
              }
          });

          previewText += `\\nSUMMARY:\\nPromote: ${promoteCount}\\nRepeat: ${repeatCount}\\nTransfer: ${transferCount}\\nGraduate: ${graduateCount}`;

          alert(previewText);
      }

      function exportPromotionReport() {
          const reportData = {
              timestamp: new Date().toISOString(),
              academic_year: promotionData.academic_year,
              total_students: promotionData.promotion_summary.total_students,
              selected_students: Array.from(selectedStudents.entries()).map(([id, data]) => ({
                  id: id,
                  name: data.name,
                  admission_number: data.admission_number,
                  current_grade: data.grade_name,
                  action: document.querySelector(`.promotion-select[data-student-id="${id}"]`)?.value || 'promote'
              }))
          };

          const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `promotion_report_${new Date().toISOString().split('T')[0]}.json`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
      }

      function updateStatistics() {
          const selectedCount = selectedStudents.size;
          let promoteCount = 0, repeatCount = 0, transferCount = 0, graduateCount = 0;
          let promotionReady = 0, reviewRequired = 0;

          selectedStudents.forEach((studentData, studentId) => {
              const actionSelect = document.querySelector(`.promotion-select[data-student-id="${studentId}"]`);
              const action = actionSelect ? actionSelect.value : 'promote';

              switch(action) {
                  case 'promote':
                      promoteCount++;
                      if (studentData.can_be_promoted) promotionReady++;
                      else reviewRequired++;
                      break;
                  case 'repeat': repeatCount++; reviewRequired++; break;
                  case 'transfer': transferCount++; reviewRequired++; break;
                  case 'graduate': graduateCount++; promotionReady++; break;
              }
          });

          // Update UI counters
          document.getElementById('selected-count').textContent = selectedCount;
          document.getElementById('promotion-ready').textContent = promotionReady;
          document.getElementById('review-required').textContent = reviewRequired;
          document.getElementById('repeat-count').textContent = repeatCount;
          document.getElementById('transfer-count').textContent = transferCount;

          // Update charts
          if (charts.actionChart) {
              charts.actionChart.data.datasets[0].data = [promoteCount, repeatCount, transferCount, graduateCount];
              charts.actionChart.update();
          }
      }

      function initializeStreamSelectors() {
          // Populate stream selectors based on target grades
          document.querySelectorAll('.stream-select').forEach(select => {
              const studentId = select.dataset.studentId;
              const targetGradeSpan = document.querySelector(`.target-grade[data-student-id="${studentId}"]`);
              const targetGrade = targetGradeSpan ? targetGradeSpan.textContent.trim() : '';

              if (targetGrade && availableStreams[targetGrade]) {
                  select.innerHTML = '<option value="">Select Stream</option>';
                  availableStreams[targetGrade].forEach(stream => {
                      const option = document.createElement('option');
                      option.value = stream.id;
                      option.textContent = stream.name;
                      select.appendChild(option);
                  });
              }
          });
      }

      function initializeSelectedStudents() {
          // Initialize selected students map with eligible students
          document.querySelectorAll('.student-select:checked').forEach(checkbox => {
              const studentId = parseInt(checkbox.dataset.studentId);
              const grade = checkbox.dataset.grade;

              // Find student data
              const studentData = findStudentData(studentId, grade);
              if (studentData) {
                  selectedStudents.set(studentId, {
                      ...studentData,
                      action: studentData.is_final_grade ? 'graduate' : 'promote',
                      notes: ''
                  });
              }
          });
      }

      function findStudentData(studentId, grade) {
          if (promotionData.students_by_grade && promotionData.students_by_grade[grade]) {
              return promotionData.students_by_grade[grade].students.find(s => s.id === studentId);
          }
          return null;
      }

      function toggleGradeSelection(grade) {
          const gradeCheckbox = document.querySelector(`.grade-select-all[data-grade="${grade}"]`);
          const studentCheckboxes = document.querySelectorAll(`.student-select[data-grade="${grade}"]`);

          studentCheckboxes.forEach(checkbox => {
              checkbox.checked = gradeCheckbox.checked;
              updateStudentSelection(parseInt(checkbox.dataset.studentId));
          });
      }

      function updateStudentSelection(studentId) {
          const checkbox = document.querySelector(`.student-select[data-student-id="${studentId}"]`);
          const grade = checkbox.dataset.grade;

          if (checkbox.checked) {
              const studentData = findStudentData(studentId, grade);
              if (studentData) {
                  selectedStudents.set(studentId, {
                      ...studentData,
                      action: studentData.is_final_grade ? 'graduate' : 'promote',
                      notes: ''
                  });
              }
          } else {
              selectedStudents.delete(studentId);
          }
          updateStatistics();
      }

      function updatePromotionAction(studentId) {
          const actionSelect = document.querySelector(`.promotion-select[data-student-id="${studentId}"]`);
          const streamSelect = document.querySelector(`.stream-select[data-student-id="${studentId}"]`);
          const targetGradeSpan = document.querySelector(`.target-grade[data-student-id="${studentId}"]`);

          const action = actionSelect.value;

          // Update selected student data
          if (selectedStudents.has(studentId)) {
              selectedStudents.get(studentId).action = action;
          }

          // Show/hide stream selector based on action
          if (action === 'promote') {
              streamSelect.style.display = 'block';
              targetGradeSpan.style.display = 'inline';
          } else {
              streamSelect.style.display = 'none';
              if (action === 'repeat') {
                  targetGradeSpan.textContent = 'Same Grade';
              } else if (action === 'transfer' || action === 'graduate') {
                  targetGradeSpan.textContent = 'N/A';
              }
          }
          updateStatistics();
      }



      function processPromotions() {
          if (selectedStudents.size === 0) {
              alert('Please select at least one student for promotion.');
              return;
          }

          // Collect promotion data
          const academicYearTo = document.getElementById('academic-year').value;
          const studentsData = [];

          selectedStudents.forEach((studentData, studentId) => {
              const actionSelect = document.querySelector(`.promotion-select[data-student-id="${studentId}"]`);
              const streamSelect = document.querySelector(`.stream-select[data-student-id="${studentId}"]`);
              const notesInput = document.querySelector(`.notes-input[data-student-id="${studentId}"]`);

              const action = actionSelect.value;
              let promotionRecord = {
                  student_id: studentId,
                  action: action,
                  notes: notesInput.value.trim()
              };

              // Add target grade and stream for promotions
              if (action === 'promote') {
                  const nextGrade = studentData.next_grade;
                  if (nextGrade) {
                      // Find grade ID by name
                      const gradeId = findGradeIdByName(nextGrade);
                      if (gradeId) {
                          promotionRecord.to_grade_id = gradeId;
                          if (streamSelect.value) {
                              promotionRecord.to_stream_id = parseInt(streamSelect.value);
                          }
                      }
                  }
              }

              studentsData.push(promotionRecord);
          });

          const promotionPayload = {
              academic_year_to: academicYearTo,
              students: studentsData
          };

          // Show loading overlay
          document.getElementById('loadingOverlay').style.display = 'flex';

          // Send promotion request
          fetch('{{ url_for("admin.process_promotion") }}', {
              method: 'POST',
              headers: {
                  'Content-Type': 'application/json',
                  'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
              },
              body: JSON.stringify(promotionPayload)
          })
          .then(response => response.json())
          .then(data => {
              document.getElementById('loadingOverlay').style.display = 'none';

              if (data.success) {
                  alert(`Promotion completed successfully!\n\nProcessed: ${data.processed_count} students\nPromoted: ${data.promoted_count}\nRepeated: ${data.repeated_count}\nTransferred: ${data.transferred_count}\nGraduated: ${data.graduated_count}`);
                  location.reload(); // Refresh the page to show updated data
              } else {
                  alert(`Promotion failed: ${data.error}`);
                  if (data.errors && data.errors.length > 0) {
                      console.error('Promotion errors:', data.errors);
                  }
              }
          })
          .catch(error => {
              document.getElementById('loadingOverlay').style.display = 'none';
              alert(`Error processing promotions: ${error.message}`);
              console.error('Error:', error);
          });
      }

      function findGradeIdByName(gradeName) {
          // This would need to be populated with actual grade data
          // For now, return a placeholder - this should be enhanced with real data
          const gradeMap = {
              'Grade 1': 1, 'Grade 2': 2, 'Grade 3': 3, 'Grade 4': 4,
              'Grade 5': 5, 'Grade 6': 6, 'Grade 7': 7, 'Grade 8': 8, 'Grade 9': 9
          };
          return gradeMap[gradeName] || null;
      }

      // Update notes for selected students
      document.addEventListener('input', function(e) {
          if (e.target.classList.contains('notes-input')) {
              const studentId = parseInt(e.target.dataset.studentId);
              if (selectedStudents.has(studentId)) {
                  selectedStudents.get(studentId).notes = e.target.value.trim();
              }
          }
      });

      // Update stream selection for selected students
      document.addEventListener('change', function(e) {
          if (e.target.classList.contains('stream-select')) {
              const studentId = parseInt(e.target.dataset.studentId);
              if (selectedStudents.has(studentId)) {
                  selectedStudents.get(studentId).to_stream_id = parseInt(e.target.value) || null;
              }
          }
      });

      } // End of student promotion page check
    </script>
  </body>
</html>
