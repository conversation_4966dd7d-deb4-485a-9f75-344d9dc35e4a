<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=yes, maximum-scale=5.0"
    />
    <meta
      name="description"
      content="Login to {{ school_info.school_name or 'Hillview School' }} Management System"
    />
    <meta name="format-detection" content="telephone=no" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="theme-color" content="#667eea" />

    <title>Login - {{ school_info.school_name or 'Hillview School' }}</title>

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" />

    <!-- Modern Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Icons -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
      integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <!-- Responsive Framework -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/responsive_framework.css') }}"
    />

    <!-- Modern CSS Framework -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/modern_classteacher.css') }}"
    />

    <style>
      /* CSS Variables - Define them here to ensure they're available */
      :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --white: #ffffff;
        --gray-50: #f8fafc;
        --gray-200: #e5e7eb;
        --gray-400: #9ca3af;
        --gray-500: #6b7280;
        --gray-600: #4b5563;
        --gray-700: #374151;
        --gray-800: #1f2937;
        --space-2: 0.5rem;
        --space-3: 0.75rem;
        --space-4: 1rem;
        --space-5: 1.25rem;
        --space-6: 1.5rem;
        --space-8: 2rem;
        --space-10: 2.5rem;
        --space-12: 3rem;
        --space-16: 4rem;
        --radius-lg: 0.75rem;
        --radius-xl: 1rem;
        --radius-2xl: 1.5rem;
        --radius-full: 9999px;
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1),
          0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
          0 4px 6px -4px rgb(0 0 0 / 0.1);
        --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
          0 8px 10px -6px rgb(0 0 0 / 0.1);
        --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
      }

      /* Modern Login Page Styles */
      body {
        font-family: "Inter", system-ui, -apple-system, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        justify-content: center;
        align-items: flex-start; /* Changed from center to flex-start */
        margin: 0;
        padding: 2rem 1.5rem; /* Use fixed padding */
        position: relative;
        overflow-x: hidden; /* Only hide horizontal overflow */
        overflow-y: auto; /* Allow vertical scrolling */
      }

      /* Animated Background Elements */
      body::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(
          circle,
          rgba(255, 255, 255, 0.1) 1px,
          transparent 1px
        );
        background-size: 50px 50px;
        animation: float 20s ease-in-out infinite;
        z-index: 1;
      }

      @keyframes float {
        0%,
        100% {
          transform: translateY(0px) rotate(0deg);
        }
        50% {
          transform: translateY(-20px) rotate(180deg);
        }
      }

      .login-container {
        position: relative;
        z-index: 2;
        width: 100%;
        max-width: 480px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 1.5rem;
        padding: 2rem; /* Reduced padding */
        box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
        border: 1px solid rgba(255, 255, 255, 0.2);
        animation: slideUp 0.8s ease-out;
        /* Let container expand naturally */
        margin: 2rem auto;
        min-height: auto;
        height: auto;
        max-height: none;
      }

      .school-header {
        text-align: center;
        margin-bottom: 1.5rem; /* Reduced spacing */
      }

      .school-logo {
        width: 60px; /* Smaller logo */
        height: 60px;
        background: var(--primary-gradient);
        border-radius: var(--radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem; /* Reduced margin */
        box-shadow: var(--shadow-lg);
        animation: pulse 2s ease-in-out infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.05);
        }
      }

      .school-logo i {
        font-size: 2rem; /* Smaller icon */
        color: white;
      }

      .school-name {
        font-size: 1.75rem; /* Smaller title */
        font-weight: 800;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem; /* Reduced margin */
        line-height: 1.2;
      }

      .school-tagline {
        font-size: 0.9rem; /* Smaller tagline */
        color: var(--gray-600);
        font-weight: 500;
        margin-bottom: 1.5rem; /* Reduced margin */
      }

      .login-options {
        display: grid;
        gap: 1rem; /* Use fixed value instead of variable */
        /* Ensure all cards are visible */
        width: 100%;
        min-height: auto;
        height: auto;
        overflow: visible;
        /* Force visibility */
        grid-template-rows: repeat(3, auto);
      }

      .login-card {
        background: linear-gradient(135deg, #ffffff, #f8f9fa);
        border-radius: 1rem; /* Fixed value */
        padding: 1rem; /* Reduced padding */
        border: 2px solid transparent;
        transition: all 0.3s ease;
        text-decoration: none;
        color: inherit;
        position: relative;
        overflow: hidden;
      }

      .login-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
        transform: scaleX(0);
        transition: transform 0.3s ease;
      }

      .login-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
        border-color: rgba(102, 126, 234, 0.2);
        text-decoration: none;
        color: inherit;
      }

      .login-card:hover::before {
        transform: scaleX(1);
      }

      .login-card-header {
        display: flex;
        align-items: center;
        gap: 0.75rem; /* Reduced gap */
        margin-bottom: 0; /* Remove bottom margin */
      }

      .login-icon {
        width: 40px; /* Smaller icon */
        height: 40px;
        background: var(--primary-gradient);
        border-radius: 0.75rem; /* Fixed value */
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem; /* Smaller icon */
        box-shadow: var(--shadow-md);
      }

      .login-title {
        font-size: 1.1rem; /* Smaller title */
        font-weight: 700;
        color: var(--gray-800);
        margin: 0;
      }

      .login-description {
        color: var(--gray-600);
        font-size: 0.8rem; /* Smaller description */
        line-height: 1.4;
        margin: 0.25rem 0 0 0; /* Small top margin */
      }

      .login-arrow {
        margin-left: auto;
        color: var(--gray-400);
        font-size: 1.2rem;
        transition: all 0.3s ease;
      }

      .login-card:hover .login-arrow {
        color: var(--gray-600);
        transform: translateX(4px);
      }

      .admin-card {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
      }

      .admin-card .login-icon {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
      }

      .admin-card .login-title,
      .admin-card .login-description {
        color: white;
      }

      .admin-card .login-arrow {
        color: rgba(255, 255, 255, 0.7);
      }

      .admin-card:hover .login-arrow {
        color: white;
      }

      /* Subject Teacher Card Styling */
      .subject-teacher-card {
        background: linear-gradient(135deg, #10b981, #059669) !important;
        color: white !important;
        /* Force visibility */
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative !important;
        z-index: 10 !important;
      }

      .subject-teacher-card .login-icon {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
      }

      .subject-teacher-card .login-title,
      .subject-teacher-card .login-description {
        color: white;
      }

      .subject-teacher-card .login-arrow {
        color: rgba(255, 255, 255, 0.7);
      }

      .subject-teacher-card:hover .login-arrow {
        color: white;
      }

      .footer {
        text-align: center;
        margin-top: var(--space-8);
        padding-top: var(--space-6);
        border-top: 1px solid var(--gray-200);
      }

      .footer-text {
        color: var(--gray-500);
        font-size: 0.875rem;
        margin: 0;
      }

      .footer-links {
        margin-top: var(--space-3);
        display: flex;
        justify-content: center;
        gap: var(--space-6);
      }

      .footer-link {
        color: var(--gray-500);
        text-decoration: none;
        font-size: 0.875rem;
        transition: color 0.2s ease;
      }

      .footer-link:hover {
        color: var(--gray-700);
        text-decoration: none;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .login-container {
          margin: var(--space-4);
          padding: var(--space-8);
        }

        .school-name {
          font-size: 1.75rem;
        }

        .login-card {
          padding: var(--space-5);
        }

        .login-card-header {
          gap: var(--space-3);
        }

        .login-icon {
          width: 45px;
          height: 45px;
          font-size: 1.25rem;
        }

        .login-title {
          font-size: 1.125rem;
        }
      }

      /* Loading Animation */
      .loading {
        position: relative;
        overflow: hidden;
      }

      .loading::after {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.4),
          transparent
        );
        animation: loading 1.5s infinite;
      }

      @keyframes loading {
        0% {
          left: -100%;
        }
        100% {
          left: 100%;
        }
      }
    </style>
  </head>
  <body>
    <div class="login-container">
      <!-- School Header -->
      <div class="school-header">
        <div class="school-logo">
          {% if school_info.logo_url and school_info.logo_url !=
          '/static/images/default_logo.png' %}
          <img
            src="{{ school_info.logo_url }}"
            alt="School Logo"
            style="
              width: 60px;
              height: 60px;
              border-radius: 50%;
              object-fit: cover;
            "
          />
          {% else %}
          <i class="fas fa-graduation-cap"></i>
          {% endif %}
        </div>
        <h1 class="school-name">
          {{ school_info.school_name or 'Hillview School' }}
        </h1>
        <p class="school-tagline">
          {{ school_info.school_motto or 'Excellence in Education • Modern
          Learning' }}
        </p>
      </div>

      <!-- Login Options -->
      <div class="login-options">
        <!-- Admin Login -->
        <a
          href="{{ url_for('auth.admin_login') }}"
          class="login-card admin-card"
        >
          <div class="login-card-header">
            <div class="login-icon">
              <i class="fas fa-user-shield"></i>
            </div>
            <div>
              <h3 class="login-title">Admin Portal</h3>
              <p class="login-description">
                Headteacher & Administrative Access
              </p>
            </div>
            <div class="login-arrow">
              <i class="fas fa-arrow-right"></i>
            </div>
          </div>
        </a>

        <!-- Class Teacher Login -->
        <a href="{{ url_for('auth.classteacher_login') }}" class="login-card">
          <div class="login-card-header">
            <div class="login-icon">
              <i class="fas fa-chalkboard-teacher"></i>
            </div>
            <div>
              <h3 class="login-title">Class Teacher</h3>
              <p class="login-description">
                Class management, marks upload & reports
              </p>
            </div>
            <div class="login-arrow">
              <i class="fas fa-arrow-right"></i>
            </div>
          </div>
        </a>

        <!-- Subject Teacher Login -->
        <a
          href="{{ url_for('auth.teacher_login') }}"
          class="login-card subject-teacher-card"
        >
          <div class="login-card-header">
            <div class="login-icon">
              <i class="fas fa-user-graduate"></i>
            </div>
            <div>
              <h3 class="login-title">Subject Teacher</h3>
              <p class="login-description">
                Subject-specific teaching & assessment
              </p>
            </div>
            <div class="login-arrow">
              <i class="fas fa-arrow-right"></i>
            </div>
          </div>
        </a>
      </div>

      <!-- Footer -->
      <div class="footer">
        <p class="footer-text">
          © 2025 {{ school_info.school_name or 'Hillview School' }}. All rights
          reserved.
        </p>
        <div class="footer-links">
          <a href="#" class="footer-link">Help</a>
          <a href="#" class="footer-link">Support</a>
          <a href="#" class="footer-link">Privacy</a>
        </div>
      </div>
    </div>

    <!-- JavaScript for Enhanced Interactions -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Add click animation to login cards
        const loginCards = document.querySelectorAll(".login-card");

        loginCards.forEach((card) => {
          card.addEventListener("click", function (e) {
            // Add loading animation
            this.classList.add("loading");

            // Remove loading after navigation starts
            setTimeout(() => {
              this.classList.remove("loading");
            }, 1000);
          });

          // Enhanced hover effects
          card.addEventListener("mouseenter", function () {
            this.style.transform = "translateY(-6px) scale(1.02)";
          });

          card.addEventListener("mouseleave", function () {
            this.style.transform = "translateY(0) scale(1)";
          });
        });

        // Add keyboard navigation
        document.addEventListener("keydown", function (e) {
          if (
            e.key === "Enter" &&
            document.activeElement.classList.contains("login-card")
          ) {
            document.activeElement.click();
          }
        });

        // Animate elements on load
        const container = document.querySelector(".login-container");
        container.style.opacity = "0";
        container.style.transform = "translateY(20px)";

        setTimeout(() => {
          container.style.transition = "all 0.6s ease-out";
          container.style.opacity = "1";
          container.style.transform = "translateY(0)";
        }, 100);
      });
    </script>

    <!-- Responsive Framework JavaScript -->
    <script src="{{ url_for('static', filename='js/responsive_utils.js') }}"></script>
  </body>
</html>
